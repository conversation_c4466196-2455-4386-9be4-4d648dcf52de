import numpy as np

# Define a class to store user data
class Person:
    def __init__(self, NA, AG, SX, HT, WE, PU, EX, DF, GL, DT):
        self.NA = NA     # Name
        self.AG = AG     # Age
        self.SX = SX     # Sex (1 = Male, 0 = Female)
        self.HT = HT     # Height (cm)
        self.WE = WE     # Body weight (kg)
        self.PU = PU     # Clean pushups
        self.EX = EX     # Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced)
        self.DF = DF     # Difficulty (-1 = Easy, 0 = Normal, 1 = Hard)
        self.GL = GL     # Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training)
        self.DT = DT     # Diet Type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg)

# Function to collect input and return a Person object
def collect_user_data():
    print("Enter your details:\n")

    NA = "Ankit"
    AG = 21
    SX = int(input("Sex (1 = Male, 0 = Female): "))
    HT = 200
    WE = int(input("Body Weight (kg): "))
    PU = 100
    EX = int(input("Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced): "))
    DF = 0
    GL = int(input("Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training): "))
    DT = 1

    return Person(NA, AG, SX, HT, WE, PU, EX, DF, GL, DT)



class ExerciseEstimator:

    def __init__(self, base_factor, sex_male, sex_female, experience_factor, rounding=5, min_capacity=None, **kwargs):
        self.base_factor = base_factor
        self.sex_male = sex_male
        self.sex_female = sex_female
        self.experience_factor = experience_factor
        self.rounding = rounding
        self.min_capacity = min_capacity

    def estimate(self, WE, SX, EX):
        sex_factor = self.sex_male if SX == 1 else self.sex_female
        value = WE * self.base_factor * sex_factor + EX * self.experience_factor
        value = round(value / self.rounding) * self.rounding
        
        if self.min_capacity is not None and value < self.min_capacity:
            value = self.min_capacity
        return value

exercise_config = {
    # ---------------- Back ----------------
    "Waited_Pull_up": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 3]
    },
    "Neutral_Grip_Pull_up": {
        "base_factor":     0.28,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 6.0,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 3]
    },
    "Chin_up": {
        "base_factor":     0.32,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 7.0,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 3]
    },
    "Barbell_Row": {
        "base_factor":     0.65,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [1, 2, 2]
    },
    "Pendlay_Row": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [1, 2, 2]
    },
    "Dumbbell_Row": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Back",
        "muscle_sub_group": [1, 2, 2]
    },
    "Chest_Supported_T_Bar_Row": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [1, 2, 2]
    },
    "Cable_Row": {
        "base_factor":     0.55,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [1, 2, 2]
    },
    "Wide_Grip_Pull_up_A": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 6.0,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 3]
    },
    "Wide_Grip_Pull_up_B": {
        "base_factor":     0.27,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 6.5,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 3]
    },
    "Wide_Grip_Lat_Pull_down": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 3]
    },
    "Neutral_Grip_Lat_Pull_down": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 3]
    },
    "Half_Kneeling_1_Arm_Lat_Pull_down_A": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 8,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 2]
    },
    "Half_Kneeling_1_Arm_Lat_Pull_down_B": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 2]
    },
    "Cable_Lat_Pullover": {
        "base_factor":     0.45,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [3, 3, 2]
    },
    "Meadows_Row": {
        "base_factor":     0.5,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [1, 2, 2]
    },
    "Free_Standing_T_Bar_Row": {
        "base_factor":     0.55,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [1, 2, 2]
    },
    "Wide_Grip_Cable_Row": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back",
        "muscle_sub_group": [1, 2, 2]
    },
    "Barbell_Shrug": {
        "base_factor": 0.20,
        "sex_male": 1.0,
        "sex_female": 0.70,
        "experience_factor": 4.0,
        "rounding": 5.0,
        "muscle_group": "Back",
        "muscle_sub_group": [1,1,1]
    },
    "Face_Pull": {
        "base_factor": 0.14,
        "sex_male": 1.0,
        "sex_female": 0.80,
        "experience_factor": 3.0,
        "rounding": 2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [3,3,3]
    },
    
    # ---------------- Biceps ----------------
    "Dumbbell_Preacher_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Machine_Preacher_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 3.0,
        "rounding":        5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Scott_Curl": {
        "base_factor":     0.17,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Incline_Curl": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [1, 1, 1]
    },
    "EZ_Bar_Curl": {
        "base_factor":     0.20,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 3.0,
        "rounding":        5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Barbell_Curl": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 3.5,
        "rounding":        5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Twist_Dumbbell_Curl": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Hammer_Preacher_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Spider_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Strict_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Face_Away_Bayesian_Cable_Curl": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [1, 1, 1]
    },
    "Standard_Dumbbell_Curl": {
        "base_factor":     0.17,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Dumbbell_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Hammer_Curl": {
        "base_factor":     0.19,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps",
        "muscle_sub_group": [1, 2, 2]
    },
    
    # ---------------- Chest ----------------
    "Bench_Press": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Inclined_Dumbbell_Press": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Chest",
        "muscle_sub_group": [1, 1, 1]
    },
    "Decline_Bench_Press": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Chest",
        "muscle_sub_group": [3, 3, 3]
    },
    "Flat_Dumbbell_Press": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Cable_Crossover": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Pec_Deck": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Dembbell_Flye": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Hex_Press": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Guillottine_Press": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Chest",
        "muscle_sub_group": [1, 1, 1]
    },
    "Push_up": {
        "base_factor":     0.18,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 6.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Deficit_Push_up": {
        "base_factor":     0.15,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 5.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Plyometric_Push_up": {
        "base_factor":     0.12,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 4.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "1_Arm_Dumbbell_Press": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5.0,
        "rounding":        2.5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Smith_Machine_Flat_Bench_Press": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Smith_Machine_Incline_Bench_Press": {
        "base_factor":     0.65,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Chest",
        "muscle_sub_group": [1, 1, 1]
    },
    "Machine_Chest_Press": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },
    "Dumbbell_Pullover": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Chest",
        "muscle_sub_group": [2, 2, 2]
    },

    # ---------------- Deadlift ----------------
    "Classic_Deadlift": {
        "base_factor":     0.6,
        "experience_factor": 10.0,
        "sex_male":        1.5,
        "sex_female":      1.0,
        "rounding":        5,
        "muscle_group": "Deadlift",
        "muscle_sub_group": [1, 1, 1]
    },
    "Sumo_Deadlift": {
        "base_factor":     0.65,
        "experience_factor": 8.75,
        "sex_male":        1.4,
        "sex_female":      0.95,
        "rounding":        5,
        "muscle_group": "Deadlift",
        "muscle_sub_group": [2, 2, 2]
    },

    # ---------------- Legs ----------------
    "Barbell_Back_Squat": {
        "base_factor":     0.85,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 2, 3]
    },
    "Barbell_Front_Squat": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 1, 2]
    },
    "Low_Bar_Squat": {
        "base_factor":     0.9,
        "sex_male":        1.0,
        "sex_female":      0.7,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 2, 3]
    },
    "Hack_Squat": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 20,
        "rounding":        5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 1, 3]
    },
    "45_Degree_Leg_Press": {
        "base_factor":     1.2,
        "sex_male":        1.0,
        "sex_female":      0.7,
        "experience_factor": 25,
        "rounding":        10,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 3, 3]
    },
    "Lunge": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 2, 3]
    },
    "Leg_Extension": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 1, 1]
    },
    "Goblet_Squat": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 1, 2]
    },
    "Bulgarian_Split_Squat": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 2, 2]
    },
    "Step_up": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 2, 3]
    },
    "Smith_Machine_Squat": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Legs",
        "muscle_sub_group": [1, 1, 2]
    },
    "Standing_Calf_Raise": {
        "base_factor": 0.22,
        "sex_male": 1.0,
        "sex_female": 0.85,
        "experience_factor": 2.5,
        "rounding": 5.0,
        "muscle_group": "Legs",
        "muscle_sub_group": [3,3,3]
    },
    "Lying_Leg_Curl": {
        "base_factor": 0.16,
        "sex_male": 1.0,
        "sex_female": 0.75,
        "experience_factor": 2.5,
        "rounding": 2.5,
        "muscle_group": "Legs",
        "muscle_sub_group": [2,2,2]
    },

    # ---------------- Shoulder ----------------
    "Overhead_Press": {
        "base_factor":     0.5,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [1, 1, 2]
    },
    "Dumbbell_Overhead_Press": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [1, 1, 2]
    },
    "Overhead_Seated_Press": {
        "base_factor":     0.45,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [1, 1, 2]
    },
    "Standing_Dumbbell_Lateral_Raise": {
        "base_factor":     0.12,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [2, 2, 2]
    },
    "Cable_Lateral_Raise": {
        "base_factor":     0.10,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [2, 2, 2]
    },
    "Lean_Away_Dumbbell_Lateral_Raise": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 3.0,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [2, 2, 2]
    },
    "Lean_In_Dumbbell_Lateral_Raise": {
        "base_factor":     0.13,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [2, 2, 2]
    },
    "Atlantis_Standing_Machine_Lateral_Raise": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [2, 2, 2]
    },
    "Seated_Machine_Lateral_Raise": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [2, 2, 2]
    },
    "Super_Row_Lateral_Raise": {
        "base_factor":     0.11,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [2, 2, 2]
    },
    "Arnold_Style_Side_Lying_Dumbbell_Press": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [1, 1, 2]
    },
    "Reverse_Pec_Deck": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [3, 3, 3]
    },
    "Rope_Facepull": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [3, 3, 3]
    },
    "Bent_Over_Reverse_Dumbbell_Flye": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [3, 3, 3]
    },
    "Upright_Row": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [2, 2, 2]
    },
    "Cable_Y_Raise": {
        "base_factor":     0.1,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [3, 3, 3]
    },
    "Front_Raise": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 3.0,
        "rounding":        2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [1, 1, 1]
    },
    "Barbell_Front_Raise": {
        "base_factor": 0.15,
        "sex_male": 1.0,
        "sex_female": 0.60,
        "experience_factor": 3.0,
        "rounding": 2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [1,1,1]
    },
    "Dumbbell_Front_Raise": {
        "base_factor": 0.12,
        "sex_male": 1.0,
        "sex_female": 0.65,
        "experience_factor": 2.5,
        "rounding": 2.5,
        "muscle_group": "Shoulder",
        "muscle_sub_group": [1,1,1]
    },
    # ---------------- Triceps ----------------
    "Close_Grip_Dips": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Overhead_Cable_Triceps_Extension": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 1]
    },
    "Overhead_Cable_Triceps_Extension_Rope": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 1]
    },
    "Triceps_Press_down": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Triceps_Press_down_Bar": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Triceps_Press_down_Reverse_Grip": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Skull_crusher": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Dumbbell_Skull_crusher": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "JM_Press": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Dumbbell_French_Press": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 1]
    },
    "1_Arm_Dumbbell_Overhead_Extention": {
        "base_factor":     0.12,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 3.0,
        "rounding":        2.5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 1]
    },
    "Cable_Triceps_Kickback": {
        "base_factor":     0.10,
        "sex_male":        1.0,
        "sex_female":      0.4,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Dumbbell_Triceps_Kickback": {
        "base_factor":     0.11,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Bench_Dips": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [1, 1, 2]
    },
    "Close_Grip_Push_up": {
        "base_factor":     0.16,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 5.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [2, 2, 2]
    },
    "Diamond_Push_up": {
        "base_factor":     0.14,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 4.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Triceps",
        "muscle_sub_group": [2, 2, 2]
    },
    # ---------------- Forearms ----------------
    "Barbell_Wrist_Curl": {
        "base_factor": 0.10,
        "experience_factor": 2.5,
        "sex_male": 1.0,
        "sex_female": 0.7,
        "rounding": 2.5,
        "muscle_group": "Forearms",
        "muscle_sub_group": [1,1,1]  # Inner forearms (wrist flexors)
    },

    "Reverse_Barbell_Wrist_Curl": {
        "base_factor": 0.08,
        "experience_factor": 2.0,
        "sex_male": 1.0,
        "sex_female": 0.65,
        "rounding": 2.5,
        "muscle_group": "Forearms",
        "muscle_sub_group": [2,2,2]  # Outer forearms (wrist extensors)
    },

    "Farmers_Walk": {
        "base_factor": 0.25,
        "experience_factor": 4.0,
        "sex_male": 1.2,
        "sex_female": 0.8,
        "rounding": 5.0,
        "muscle_group": "Forearms",
        "muscle_sub_group": [3,3,3]  # Grip (isometric)
    },

    "Plate_Pinch": {
        "base_factor": 0.06,
        "experience_factor": 3.0,
        "sex_male": 1.0,
        "sex_female": 0.75,
        "rounding": 2.5,
        "muscle_group": "Forearms",
        "muscle_sub_group": [3,3,3]  # Grip (pinch/crush)
    },

    "Reverse_Barbell_Curl": {
        "base_factor": 0.12,
        "experience_factor": 3.5,
        "sex_male": 1.1,
        "sex_female": 0.7,
        "rounding": 2.5,
        "muscle_group": "Forearms",
        "muscle_sub_group": [2,2,2]  # Outer forearms (brachioradialis)
    }

}

exercise_names = list(exercise_config.keys())


exercise_weights = {
    "Back": [
        0.20,  # Waited_Pull_up
        0.18,  # Neutral_Grip_Pull_up
        0.16,  # Chin_up
        0.14,  # Barbell_Row
        0.12,  # Pendlay_Row
        0.10,  # Dumbbell_Row
        0.08,  # Chest_Supported_T_Bar_Row
        0.06,  # Cable_Row
        0.05,  # Wide_Grip_Pull_up_A
        0.04,  # Wide_Grip_Pull_up_B
        0.03,  # Wide_Grip_Lat_Pull_down
        0.03,  # Neutral_Grip_Lat_Pull_down
        0.02,  # Half_Kneeling_1_Arm_Lat_Pull_down_A
        0.02,  # Half_Kneeling_1_Arm_Lat_Pull_down_B
        0.02,  # Cable_Lat_Pullover
        0.01,  # Meadows_Row
        0.01,  # Free_Standing_T_Bar_Row
        0.01,  # Wide_Grip_Cable_Row
        0.04,  # Barbell_Shrug (new)
        0.03   # Face_Pull (new)
    ],
    "Biceps": [
        0.25,  # Barbell_Curl (barbell ↑)
        0.20,  # EZ_Bar_Curl (barbell ↑)
        0.18,  # Dumbbell_Preacher_Curl
        0.15,  # Incline_Curl (dumbbell ↑)
        0.10,  # Hammer_Curl (dumbbell ↑)
        0.08,  # Strict_Curl
        0.06,  # Scott_Curl (machine ↓)
        0.05,  # Machine_Preacher_Curl (machine ↓)
        0.04,  # Twist_Dumbbell_Curl
        0.03,  # Spider_Curl
        0.02,  # Hammer_Preacher_Curl
        0.02,  # Standard_Dumbbell_Curl
        0.01,  # Dumbbell_Curl
        0.01   # Face_Away_Bayesian_Cable_Curl
    ],
    "Chest": [
        0.28,  # Bench_Press (barbell compound ↑)
        0.22,  # Inclined_Dumbbell_Press (dumbbell ↑)
        0.18,  # Flat_Dumbbell_Press (dumbbell ↑)
        0.12,  # Decline_Bench_Press
        0.08,  # Push_up (bodyweight ↑)
        0.05,  # Guillottine_Press (specialized ↓)
        0.04,  # Hex_Press
        0.03,  # Cable_Crossover
        0.02,  # Plyometric_Push_up
        0.02,  # Deficit_Push_up
        0.01,  # 1_Arm_Dumbbell_Press
        0.01,  # Smith_Machine_Flat_Bench_Press (machine ↓)
        0.01,  # Smith_Machine_Incline_Bench_Press (machine ↓)
        0.01,  # Machine_Chest_Press (machine ↓)
        0.01,  # Pec_Deck (machine ↓)
        0.01,  # Dembbell_Flye (typo corrected)
        0.00   # Dumbbell_Pullover
    ],
    "Deadlift": [
        0.65,  # Classic_Deadlift
        0.35,  # Sumo_Deadlift
    ],
    "Legs": [
        0.30,  # Barbell_Back_Squat
        0.25,  # Barbell_Front_Squat
        0.18,  # Low_Bar_Squat
        0.12,  # Hack_Squat
        0.08,  # 45_Degree_Leg_Press
        0.05,  # Lunge
        0.04,  # Leg_Extension
        0.03,  # Goblet_Squat
        0.02,  # Bulgarian_Split_Squat
        0.01,  # Step_up
        0.01,  # Smith_Machine_Squat
        0.06,  # Standing_Calf_Raise (new)
        0.07   # Lying_Leg_Curl (new)
    ],
    "Shoulder": [
        0.25,  # Overhead_Press
        0.20,  # Dumbbell_Overhead_Press
        0.15,  # Overhead_Seated_Press
        0.12,  # Standing_Dumbbell_Lateral_Raise
        0.10,  # Cable_Lateral_Raise
        0.08,  # Lean_Away_Dumbbell_Lateral_Raise
        0.07,  # Lean_In_Dumbbell_Lateral_Raise
        0.06,  # Atlantis_Standing_Machine_Lateral_Raise
        0.05,  # Seated_Machine_Lateral_Raise
        0.04,  # Super_Row_Lateral_Raise
        0.03,  # Arnold_Style_Side_Lying_Dumbbell_Press
        0.03,  # Reverse_Pec_Deck
        0.02,  # Rope_Facepull
        0.02,  # Bent_Over_Reverse_Dumbbell_Flye
        0.01,  # Upright_Row
        0.01,  # Cable_Y_Raise
        0.01,  # Front_Raise
        0.02,  # Barbell_Front_Raise
        0.02   # Dumbbell_Front_Raise

    ],
    "Triceps": [
        0.25,  # Close_Grip_Dips (compound ↑)
        0.20,  # Close_Grip_Push_up (compound ↑)
        0.18,  # JM_Press (barbell ↑)
        0.15,  # Skull_crusher (EZ-bar ↑)
        0.12,  # Overhead_Cable_Triceps_Extension
        0.10,  # Dumbbell_French_Press
        0.08,  # Overhead_Cable_Triceps_Extension_Rope
        0.06,  # Triceps_Press_down
        0.05,  # Dumbbell_Skull_crusher
        0.04,  # Triceps_Press_down_Bar
        0.02,  # 1_Arm_Dumbbell_Overhead_Extention
        0.02,  # Triceps_Press_down_Reverse_Grip
        0.01,  # Cable_Triceps_Kickback
        0.01,  # Dumbbell_Triceps_Kickback
        0.01,  # Bench_Dips
        0.00   # Diamond_Push_up (renamed from Demand_Push_up)
    ],
    "Forearms": [
        0.25,  # Farmers_Walk
        0.12,  # Reverse_Barbell_Curl
        0.10,  # Barbell_Wrist_Curl
        0.08,  # Reverse_Barbell_Wrist_Curl
        0.06   # Plate_Pinch
    ]
}


def choose(muscle_group, n):
    elements = [ex for ex, cfg in exercise_config.items() if cfg.get("muscle_group") == muscle_group]
    # If you have weights for the muscle group, you can use them here
    weights = np.ones(len(elements)) / len(elements)  # or use custom weights if available
    selected_exercises = [str(e) for e in np.random.choice(elements, size=n, replace=False, p=weights)]

    # Create a mapping of exercise names to their weights for sorting
    exercise_weight_map = {}
    if muscle_group in exercise_weights:
        # Get the exercise names in the order they appear in exercise_weights
        muscle_exercises = [ex for ex, cfg in exercise_config.items() if cfg.get("muscle_group") == muscle_group]
        weights_list = exercise_weights[muscle_group]

        # Create mapping - match exercises to their weights
        for i, exercise in enumerate(muscle_exercises):
            if i < len(weights_list):
                exercise_weight_map[exercise] = weights_list[i]
            else:
                exercise_weight_map[exercise] = 0.01  # Default small weight for unmapped exercises
    else:
        # If no weights defined for this muscle group, assign equal weights
        for exercise in selected_exercises:
            exercise_weight_map[exercise] = 1.0

    # Sort selected exercises by their weights in descending order (highest weight first)
    selected_exercises.sort(key=lambda x: exercise_weight_map.get(x, 0.01), reverse=True)

    return selected_exercises
# Convert configs to estimator objects
estimators = {ex: ExerciseEstimator(**cfg) for ex, cfg in exercise_config.items()}

def estimate_exercise(name, WE, SX, EX):
    return estimators[name].estimate(WE, SX, EX)



# Generate workout plan as a 2D array with fixed 3 rows and 6 columns
def generate_plan_2d(user):
    if user.GL == 1:  # Weight Gain
        split = [
            "Back, Biceps, Forearms",                 # Lower Back, Biceps, Forearms
            "Chest, Shoulders, Triceps",              # Chest (All), Shoulders, Triceps
            "Legs, Abs, Treadmill",                   # Quads, Hamstrings, Calves, Abs, Treadmill
            "Back, Biceps, Forearms",                 # Upper Back, Biceps, Forearms
            "Chest, Shoulders, Triceps",              # Chest (All), Shoulders, Triceps
            "Legs, Abs, Treadmill"                    # Glutes, Quads, Hamstrings, Abs, Treadmill
        ]

    elif user.GL == 2:  # Weight Loss
        split = [
            "Back, Biceps",
            "Chest, Triceps",
            "Legs, Abs",
            "Shoulders, Forearms",
            "Treadmill, Planks",
            "Legs, Forearms"
        ]
    elif user.GL == 3:  # Strength Training
        split = [
            "Chest",
            "Deadlift",
            "Biceps, Triceps, Forearms",
            "Back",
            "Shoulders",
            "Legs"
        ]
    else:
        return []

    # Split each day's string by commas, strip spaces
    columns = [ [ex.strip() for ex in day.split(',')] for day in split ]

    # Fixed rows = 3, columns = 6
    rows = 3
    cols = 6

    plan_2d = []
    for r in range(rows):
        row_data = []
        for c in range(cols):
            # Check if the column has enough exercises
            if c < len(columns) and r < len(columns[c]):
                row_data.append(columns[c][r])
            else:
                row_data.append("XXX")  # Fill with "XXX" if no exercise here
        plan_2d.append(row_data)

    return plan_2d

# Simple 0-based indexing function to get exercise
def get_exercise(plan_2d, row, col):
    return plan_2d[row][col]

def Detailed_Plan(plan_2d):
    
    Detailed_Day_Plan = {}

    # Process each day (6 days total)
    for day in range(6):
        day_key = f"Day_{day + 1}"
        Detailed_Day_Plan[day_key] = {}

        # Get all muscle groups for this day (column in plan_2d)
        day_muscle_groups = []
        for row in range(len(plan_2d)):
            muscle_group = plan_2d[row][day]
            if muscle_group != "XXX":  # Skip placeholder entries
                day_muscle_groups.append(muscle_group)

        # Remove duplicates while preserving order
        unique_muscle_groups = []
        for mg in day_muscle_groups:
            if mg not in unique_muscle_groups:
                unique_muscle_groups.append(mg)

        # For each unique muscle group, select 3 exercises using choose()
        for muscle_group in unique_muscle_groups:
            # Clean muscle group name (remove extra spaces, handle special cases)
            clean_muscle_group = muscle_group.strip()

            # Handle special cases and non-exercise entries
            if clean_muscle_group in ["Abs", "Treadmill", "Planks", "Hanging_Leg_Raise",
                                    "Plank", "Hyperextension", "Romanian_Deadlift"]:
                # For non-standard muscle groups, use single entries
                if clean_muscle_group == "Abs":
                    Detailed_Day_Plan[day_key][clean_muscle_group] = ["Plank", "Rest", "Rest"]
                elif clean_muscle_group == "Treadmill":
                    Detailed_Day_Plan[day_key][clean_muscle_group] = ["Treadmill", "Rest", "Rest"]
                elif clean_muscle_group == "Planks":
                    Detailed_Day_Plan[day_key][clean_muscle_group] = ["Plank", "Rest", "Rest"]
                else:
                    Detailed_Day_Plan[day_key][clean_muscle_group] = [clean_muscle_group, "Rest", "Rest"]
            else:
                # For standard muscle groups, use choose() function
                try:
                    selected_exercises = choose(clean_muscle_group, 3)
                    Detailed_Day_Plan[day_key][clean_muscle_group] = selected_exercises
                except:
                    # Fallback if muscle group not found in exercise_config
                    available_exercises = [ex for ex, cfg in exercise_config.items()
                                         if cfg.get("muscle_group") == clean_muscle_group]
                    if len(available_exercises) >= 3:
                        Detailed_Day_Plan[day_key][clean_muscle_group] = available_exercises[:3]
                    elif len(available_exercises) > 0:
                        # Pad with available exercises and "Rest"
                        selected = available_exercises[:]
                        while len(selected) < 3:
                            selected.append("Rest")
                        Detailed_Day_Plan[day_key][clean_muscle_group] = selected
                    else:
                        # No exercises found, fill with Rest
                        Detailed_Day_Plan[day_key][clean_muscle_group] = ["Rest", "Rest", "Rest"]

    return Detailed_Day_Plan


if __name__ == "__main__":
    user = collect_user_data()
    plan_2d = generate_plan_2d(user)

    print(f"\nWorkout plan for {user.NA} (Goal: {user.GL}):\n")
    print("Days ->    1           2           3           4           5           6")

    for row in plan_2d:
        print('   '.join(f"{ex:<12}" for ex in row))

    # Examples
    print("\nExamples:")
    print("Exercise at split[2,1] (row 2, day 1):", get_exercise(plan_2d, 2, 1))
    print("Exercise at split[0,0] (row 0, day 0):", get_exercise(plan_2d, 0, 0))

    print(choose("Biceps", 3))  # Output: 3 weighted random back exercises
    print(choose("Biceps", 3))  # Output: 3 weighted random back exercises
    print(choose("Biceps", 3))  # Output: 3 weighted random back exercises

    # Test Detailed_Plan function
    print("\n" + "="*70)
    print("DETAILED_PLAN() FUNCTION DEMONSTRATION")
    print("="*70)

    # Call the function
    Detailed_Day_Plan = Detailed_Plan(plan_2d)

    print(f"\nInput: plan_2d from generate_plan_2d(user)")
    print(f"Output: Detailed_Day_Plan dictionary with muscle groups and 3 exercises each\n")

    # Show complete output
    print("=== COMPLETE DETAILED_DAY_PLAN OUTPUT ===")
    for day_key, muscle_groups in Detailed_Day_Plan.items():
        print(f"\n{day_key}:")
        for muscle_group, exercises in muscle_groups.items():
            print(f"  {muscle_group}: {exercises}")

    # Show detailed breakdown for Day_1
    print(f"\n=== DETAILED BREAKDOWN FOR DAY 1 ===")
    day_1_plan = Detailed_Day_Plan["Day_1"]
    print(f"Detailed_Day_Plan['Day_1'] = {day_1_plan}")
    print("\nMuscle Group Analysis:")
    for muscle_group, exercises in day_1_plan.items():
        print(f"\n  Muscle Group: {muscle_group}")
        print(f"  Selected Exercises: {exercises}")
        for i, exercise in enumerate(exercises, 1):
            if exercise != "Rest" and exercise in exercise_config:
                muscle_sub_group = exercise_config[exercise].get("muscle_sub_group", [0,0,0])
                estimated_weight = estimate_exercise(exercise, user.WE, user.SX, user.EX)
                print(f"    Exercise {i}: {exercise}")
                print(f"      - Muscle Sub-Group: {muscle_sub_group}")
                print(f"      - Estimated Weight: {estimated_weight} kg")
            else:
                print(f"    Exercise {i}: {exercise}")



