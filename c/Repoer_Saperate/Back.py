

class Person:
    def __init__(self, NA, AG, SX, HT, WE, PU, EX, DF, GL, DT):
        self.NA = NA     # Name
        self.AG = AG     # Age
        self.SX = SX     # Sex (1 = Male, 0 = Female)
        self.HT = HT     # Height (cm)
        self.WE = WE     # Body weight (kg)
        self.PU = PU     # Clean pushups
        self.EX = EX     # Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced)
        self.DF = DF     # Difficulty (-1 = Easy, 0 = Normal, 1 = Hard)
        self.GL = GL     # Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training)
        self.DT = DT     # Diet Type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg)

def collect_user_data():
    print("Enter your details:\n")

    NA = "Ankit"
    AG = int(21)
    SX = int(input("Sex (1 = Male, 0 = Female): "))
    HT = 200
    WE = int(input("Body Weight (kg): "))
    PU = 100
    EX = int(input("Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced): "))
    DF = 0
    GL = int(input("Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training): "))
    DT = 1

    return Person(NA, AG, SX, HT, WE, PU, EX, DF, GL, DT)

class ExerciseEstimator:
    def __init__(self,
                 base_factor,
                 sex_male,
                 sex_female,
                 experience_factor,
                 rounding=5,
                 min_capacity=None):
        
        self.base_factor       = base_factor
        self.sex_male          = sex_male
        self.sex_female        = sex_female
        self.experience_factor = experience_factor
        self.rounding          = rounding
        self.min_capacity      = min_capacity

    def estimate(self, WE, SX, EX):

        # Choose sex factor
        sex_factor = self.sex_male if SX == 1 else self.sex_female

        # Calculate base value
        value = WE * self.base_factor * sex_factor

        # Add experience
        value += EX * self.experience_factor

        # Round to nearest rounding
        value = round(value / self.rounding) * self.rounding

        # Enforce minimum if specified
        if self.min_capacity is not None and value < self.min_capacity:
            value = self.min_capacity
        return value

exercise_config = {
    # ---------------- Back ----------------
    "Waited_Pull_up": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Neutral_Grip_Pull_up": {
        "base_factor":     0.28,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 6.0,
        "rounding":        5
    },
    "Chin_up": {
        "base_factor":     0.32,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 7.0,
        "rounding":        5
    },
    "Barbell_Row": {
        "base_factor":     0.65,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Pendlay_Row": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Dumbbell_Row": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5
    },
    "Chest_Supported_T_Bar_Row": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Cable_Row": {
        "base_factor":     0.55,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Wide_Grip_Pull_up_A": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 6.0,
        "rounding":        5
    },
    "Wide_Grip_Pull_up_B": {
        "base_factor":     0.27,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 6.5,
        "rounding":        5
    },
    "Wide_Grip_Lat_Pull_down": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5
    },
    "Neutral_Grip_Lat_Pull_down": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 15,
        "rounding":        5
    },
    "Half_Kneeling_1_Arm_Lat_Pull_down_A": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 8,
        "rounding":        5
    },
    "Half_Kneeling_1_Arm_Lat_Pull_down_B": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Cable_Lat_Pullover": {
        "base_factor":     0.45,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Meadows_Row": {
        "base_factor":     0.5,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Free_Standing_T_Bar_Row": {
        "base_factor":     0.55,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Wide_Grip_Cable_Row": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
}

# Create estimator objects
estimators = {ex: ExerciseEstimator(**params) for ex, params in exercise_config.items()}

def estimate_exercise(exercise_name, WE, SX, EX):
    return estimators[exercise_name].estimate(WE, SX, EX)

if __name__ == "__main__":
    person = collect_user_data()
    
   # Back
print("Waited Pull up:", estimate_exercise("Waited_Pull_up", person.WE, person.SX, person.EX))
print("Neutral Grip Pull up:", estimate_exercise("Neutral_Grip_Pull_up", person.WE, person.SX, person.EX))
print("Chin up:", estimate_exercise("Chin_up", person.WE, person.SX, person.EX))
print("Barbell Row:", estimate_exercise("Barbell_Row", person.WE, person.SX, person.EX))
print("Pendlay Row:", estimate_exercise("Pendlay_Row", person.WE, person.SX, person.EX))
print("Dumbbell Row:", estimate_exercise("Dumbbell_Row", person.WE, person.SX, person.EX))
print("Chest Supported T Bar Row:", estimate_exercise("Chest_Supported_T_Bar_Row", person.WE, person.SX, person.EX))
print("Cable Row:", estimate_exercise("Cable_Row", person.WE, person.SX, person.EX))
print("Wide Grip Pull up A:", estimate_exercise("Wide_Grip_Pull_up_A", person.WE, person.SX, person.EX))
print("Wide Grip Pull up B:", estimate_exercise("Wide_Grip_Pull_up_B", person.WE, person.SX, person.EX))
print("Wide Grip Lat Pull down:", estimate_exercise("Wide_Grip_Lat_Pull_down", person.WE, person.SX, person.EX))
print("Neutral Grip Lat Pull down:", estimate_exercise("Neutral_Grip_Lat_Pull_down", person.WE, person.SX, person.EX))
print("Half Kneeling 1 Arm Lat Pull down A:", estimate_exercise("Half_Kneeling_1_Arm_Lat_Pull_down_A", person.WE, person.SX, person.EX))
print("Half Kneeling 1 Arm Lat Pull down B:", estimate_exercise("Half_Kneeling_1_Arm_Lat_Pull_down_B", person.WE, person.SX, person.EX))
print("Cable Lat Pullover:", estimate_exercise("Cable_Lat_Pullover", person.WE, person.SX, person.EX))
print("Meadows Row:", estimate_exercise("Meadows_Row", person.WE, person.SX, person.EX))
print("Free Standing T Bar Row:", estimate_exercise("Free_Standing_T_Bar_Row", person.WE, person.SX, person.EX))
print("Wide Grip Cable Row:", estimate_exercise("Wide_Grip_Cable_Row", person.WE, person.SX, person.EX))

