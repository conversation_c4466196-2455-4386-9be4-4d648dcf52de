

class Person:
    def __init__(self, NA, AG, SX, HT, WE, PU, EX, DF, GL, DT):
        self.NA = NA     # Name
        self.AG = AG     # Age
        self.SX = SX     # Sex (1 = Male, 0 = Female)
        self.HT = HT     # Height (cm)
        self.WE = WE     # Body weight (kg)
        self.PU = PU     # Clean pushups
        self.EX = EX     # Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced)
        self.DF = DF     # Difficulty (-1 = Easy, 0 = Normal, 1 = Hard)
        self.GL = GL     # Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training)
        self.DT = DT     # Diet Type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg)

def collect_user_data():
    print("Enter your details:\n")

    NA = "Ankit"
    AG = 21
    SX = int(input("Sex (1 = Male, 0 = Female): "))
    HT = 200
    WE = int(input("Body Weight (kg): "))
    PU = 100
    EX = int(input("Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced): "))
    DF = 0
    GL = int(input("Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training): "))
    DT = 1

    return Person(NA, AG, SX, HT, WE, PU, EX, DF, GL, DT)

class ExerciseEstimator:
    def __init__(self,
                 base_factor,
                 sex_male,
                 sex_female,
                 experience_factor,
                 rounding=5,
                 min_capacity=None):
        
        self.base_factor       = base_factor
        self.sex_male          = sex_male
        self.sex_female        = sex_female
        self.experience_factor = experience_factor
        self.rounding          = rounding
        self.min_capacity      = min_capacity

    def estimate(self, WE, SX, EX):
        
        # Choose sex factor
        sex_factor = self.sex_male if SX == 1 else self.sex_female

        # Calculate base value
        value = WE * self.base_factor * sex_factor

        # Add experience
        value += EX * self.experience_factor

        # Round to nearest rounding
        value = round(value / self.rounding) * self.rounding

        # Enforce minimum if specified
        if self.min_capacity is not None and value < self.min_capacity:
            value = self.min_capacity
        return value

exercise_config = {
    # ---------------- Biceps ----------------
    "Dumbbell_Preacher_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Machine_Preacher_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 3.0,
        "rounding":        5
    },
    "Scott_Curl": {
        "base_factor":     0.17,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Incline_Curl": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 2.0,
        "rounding":        2.5
    },
    "EZ_Bar_Curl": {
        "base_factor":     0.20,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 3.0,
        "rounding":        5
    },
    "Barbell_Curl": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 3.5,
        "rounding":        5
    },
    "Twist_Dumbbell_Curl": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 2.0,
        "rounding":        2.5
    },
    "Hammer_Preacher_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Spider_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Strict_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Face_Away_Bayesian_Cable_Curl": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        5
    },
    "Standard_Dumbbell_Curl": {
        "base_factor":     0.17,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        2.5
    },
    "Dumbbell_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        2.5
    },
    "Hammer_Curl": {
        "base_factor":     0.19,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 2.5,
        "rounding":        2.5
    }
}

# Create estimator objects
estimators = {ex: ExerciseEstimator(**params) for ex, params in exercise_config.items()}

def estimate_exercise(exercise_name, WE, SX, EX):
    return estimators[exercise_name].estimate(WE, SX, EX)

if __name__ == "__main__":
    person = collect_user_data()
    

    # Biceps
print("Dumbbell Preacher Curl:", estimate_exercise("Dumbbell_Preacher_Curl", person.WE, person.SX, person.EX))
print("Machine Preacher Curl:", estimate_exercise("Machine_Preacher_Curl", person.WE, person.SX, person.EX))
print("Scott Curl:", estimate_exercise("Scott_Curl", person.WE, person.SX, person.EX))
print("Incline Curl:", estimate_exercise("Incline_Curl", person.WE, person.SX, person.EX))
print("EZ Bar Curl:", estimate_exercise("EZ_Bar_Curl", person.WE, person.SX, person.EX))
print("Barbell Curl:", estimate_exercise("Barbell_Curl", person.WE, person.SX, person.EX))
print("Twist Dumbbell Curl:", estimate_exercise("Twist_Dumbbell_Curl", person.WE, person.SX, person.EX))
print("Hammer Preacher Curl:", estimate_exercise("Hammer_Preacher_Curl", person.WE, person.SX, person.EX))
print("Spider Curl:", estimate_exercise("Spider_Curl", person.WE, person.SX, person.EX))
print("Strict Curl:", estimate_exercise("Strict_Curl", person.WE, person.SX, person.EX))
print("Face Away Bayesian Cable Curl:", estimate_exercise("Face_Away_Bayesian_Cable_Curl", person.WE, person.SX, person.EX))
print("Standard Dumbbell Curl:", estimate_exercise("Standard_Dumbbell_Curl", person.WE, person.SX, person.EX))
print("Dumbbell Curl:", estimate_exercise("Dumbbell_Curl", person.WE, person.SX, person.EX))
print("Hammer Curl:", estimate_exercise("Hammer_Curl", person.WE, person.SX, person.EX))
