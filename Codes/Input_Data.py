
# Define a class to store user data
class Person:
    def __init__(self, NA, AG, SX, HT, WE, PU, EX, DF, GL, DT):
        self.NA = NA     # Name
        self.AG = AG     # Age
        self.SX = SX     # Sex (1 = Male, 0 = Female)
        self.HT = HT     # Height (cm)
        self.WE = WE     # Body weight (kg)
        self.PU = PU     # Clean pushups
        self.EX = EX     # Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced)
        self.DF = DF     # Difficulty (-1 = Easy, 0 = Normal, 1 = Hard)
        self.GL = GL     # Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training)
        self.DT = DT     # Diet Type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg)

# Function to collect input and return a Person object
def collect_user_data():
    print("Enter your details:\n")

    NA = input("Name: ")
    AG = int(input("Age (years): "))
    SX = int(input("Sex (1 = Male, 0 = Female): "))
    HT = int(input("Height (cm): "))
    WE = int(input("Body Weight (kg): "))
    PU = int(input("Number of clean pushups: "))
    EX = int(input("Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced): "))
    DF = int(input("Preferred difficulty (-1 = Easy, 0 = Normal, 1 = Hard): "))
    GL = int(input("Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training): "))
    DT = int(input("Diet type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg): "))

    return Person(NA, AG, SX, HT, WE, PU, EX, DF, GL, DT)

# Example usage
P1 = collect_user_data()

# Display the collected data
print("\n--- Collected Data ---")
print(f"Name:               {P1.NA}")
print(f"Age:                {P1.AG}")
print(f"Sex:                {P1.SX}")
print(f"Height(cm):         {P1.HT}")
print(f"Weight(kg):         {P1.WE}")
print(f"Pushups:            {P1.PU}")
print(f"Experience Level:   {P1.EX}")
print(f"Difficulty:         {P1.DF}")
print(f"Goal:               {P1.GL}")
print(f"Diet Type:          {P1.DT}")


