# Define a class to store user data
class Person:
    def __init__(self, NA, AG, SX, HT, WE, PU, EX, DF, GL, DT):
        self.NA = NA     # Name
        self.AG = AG     # Age
        self.SX = SX     # Sex (1 = Male, 0 = Female)
        self.HT = HT     # Height (cm)
        self.WE = WE     # Body weight (kg)
        self.PU = PU     # Clean pushups
        self.EX = EX     # Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced)
        self.DF = DF     # Difficulty (-1 = Easy, 0 = Normal, 1 = Hard)
        self.GL = GL     # Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training)
        self.DT = DT     # Diet Type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg)

# Function to collect input and return a Person object
def collect_user_data():
    print("Enter your details:\n")

    NA = input("Name: ")
    AG = int(input("Age (years): "))
    SX = int(input("Sex (1 = Male, 0 = Female): "))
    HT = int(input("Height (cm): "))
    WE = int(input("Body Weight (kg): "))
    PU = int(input("Number of clean pushups: "))
    EX = int(input("Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced): "))
    DF = int(input("Preferred difficulty (-1 = Easy, 0 = Normal, 1 = Hard): "))
    GL = int(input("Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training): "))
    DT = int(input("Diet type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg): "))

    return Person(NA, AG, SX, HT, WE, PU, EX, DF, GL, DT)

# Generate workout plan as a 2D array with fixed 3 rows and 6 columns
def generate_plan_2d(user):
    if user.GL == 1:  # Weight Gain
        split = [
            "Back",
            "Chest",
            "Biceps",
            "Triceps",
            "Abs, Forearms",
            "Legs"
        ]
    elif user.GL == 2:  # Weight Loss
        split = [
            "Legs, Forearms",
            "Back, Biceps",
            "Legs, Abs",
            "Shoulders, Forearms",
            "Chest, Triceps",
            "Treadmill, Planks"
        ]
    elif user.GL == 3:  # Strength Training
        split = [
            "Chest",
            "Deadlift, Lower Back",
            "Biceps, Triceps, Forearms",
            "Back Upper, Back Lower",
            "Shoulders",
            "Legs"
        ]
    else:
        return []

    # Split each day's string by commas, strip spaces
    columns = [ [ex.strip() for ex in day.split(',')] for day in split ]

    # Fixed rows = 3, columns = 6
    rows = 3
    cols = 6

    plan_2d = []
    for r in range(rows):
        row_data = []
        for c in range(cols):
            # Check if the column has enough exercises
            if c < len(columns) and r < len(columns[c]):
                row_data.append(columns[c][r])
            else:
                row_data.append("XXX")  # Fill with "XXX" if no exercise here
        plan_2d.append(row_data)

    return plan_2d

# Simple 0-based indexing function to get exercise
def get_exercise(plan_2d, row, col):
    return plan_2d[row][col]

# Main program
def main():
    user = collect_user_data()
    plan_2d = generate_plan_2d(user)

    print(f"\nWorkout plan for {user.NA} (Goal: {user.GL}):\n")
    print("Days ->    1           2           3           4           5           6")

    for row in plan_2d:
        print('   '.join(f"{ex:<12}" for ex in row))

    # Examples
    print("\nExamples:")
    print("Exercise at split[2,1] (row 2, day 1):", get_exercise(plan_2d, 2, 1))
    print("Exercise at split[0,0] (row 0, day 0):", get_exercise(plan_2d, 0, 0))

if __name__ == "__main__":
    main()
