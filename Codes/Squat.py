def predict_squat_formula(WE, SX, EX):

    base_factor = 0.85

    sex_multiplier = 0  # default
    if SX == 1:
        sex_multiplier = 1.0
    if SX == 0:
        sex_multiplier = 0.65

    # Calculate raw squat
    raw_strength = (WE * base_factor + EX * 12.5) * sex_multiplier

    # Round to nearest multiple of 5
    rounded_strength = round(raw_strength / 5) * 5

    return rounded_strength

# Example usage
WE = int(input("Enter body weight (kg): "))
SX = int(input("Enter sex (1 = male, 0 = female): "))
EX = int(input("Enter experience (0 = beginner, 1 = intermediate, 2 = advanced): "))

result = predict_squat_formula(WE, SX, EX)
print(f"Estimated Squat: {result} kg")
