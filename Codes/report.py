

class Person:
    def __init__(self, NA, AG, SX, HT, WE, PU, EX, DF, GL, DT):
        self.NA = NA     # Name
        self.AG = AG     # Age
        self.SX = SX     # Sex (1 = Male, 0 = Female)
        self.HT = HT     # Height (cm)
        self.WE = WE     # Body weight (kg)
        self.PU = PU     # Clean pushups
        self.EX = EX     # Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced)
        self.DF = DF     # Difficulty (-1 = Easy, 0 = Normal, 1 = Hard)
        self.GL = GL     # Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training)
        self.DT = DT     # Diet Type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg)

def collect_user_data():
    print("Enter your details:\n")

    NA = input("Name: ")
    AG = int(input("Age (years): "))
    SX = int(input("Sex (1 = Male, 0 = Female): "))
    HT = int(input("Height (cm): "))
    WE = int(input("Body Weight (kg): "))
    PU = int(input("Number of clean pushups: "))
    EX = int(input("Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced): "))
    DF = int(input("Preferred difficulty (-1 = Easy, 0 = Normal, 1 = Hard): "))
    GL = int(input("Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training): "))
    DT = int(input("Diet type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg): "))

    return Person(NA, AG, SX, HT, WE, PU, EX, DF, GL, DT)

class ExerciseEstimator:
    def __init__(self,
                 base_factor,
                 sex_male,
                 sex_female,
                 experience_factor,
                 rounding=5,
                 min_capacity=None):
        
        self.base_factor       = base_factor
        self.sex_male          = sex_male
        self.sex_female        = sex_female
        self.experience_factor = experience_factor
        self.rounding          = rounding
        self.min_capacity      = min_capacity

    def estimate(self, WE, SX, EX):
        # Choose sex factor
        sex_factor = self.sex_male if SX == 1 else self.sex_female
        # Calculate base value
        value = WE * self.base_factor * sex_factor
        # Add experience
        value += EX * self.experience_factor
        # Round to nearest rounding
        value = round(value / self.rounding) * self.rounding
        # Enforce minimum if specified
        if self.min_capacity is not None and value < self.min_capacity:
            value = self.min_capacity
        return value

exercise_config = {
    # ---------------- Back ----------------
    "Waited_Pull_up": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Neutral_Grip_Pull_up": {
        "base_factor":     0.28,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 6.0,
        "rounding":        5
    },
    "Chin_up": {
        "base_factor":     0.32,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 7.0,
        "rounding":        5
    },
    "Barbell_Row": {
        "base_factor":     0.65,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Pendlay_Row": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Dumbbell_Row": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5
    },
    "Chest_Supported_T_Bar_Row": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Cable_Row": {
        "base_factor":     0.55,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Wide_Grip_Pull_up_A": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 6.0,
        "rounding":        5
    },
    "Wide_Grip_Pull_up_B": {
        "base_factor":     0.27,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 6.5,
        "rounding":        5
    },
    "Wide_Grip_Lat_Pull_down": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5
    },
    "Neutral_Grip_Lat_Pull_down": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 15,
        "rounding":        5
    },
    "Half_Kneeling_1_Arm_Lat_Pull_down_A": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 8,
        "rounding":        5
    },
    "Half_Kneeling_1_Arm_Lat_Pull_down_B": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Cable_Lat_Pullover": {
        "base_factor":     0.45,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Meadows_Row": {
        "base_factor":     0.5,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Free_Standing_T_Bar_Row": {
        "base_factor":     0.55,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Wide_Grip_Cable_Row": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },

    # ---------------- Biceps ----------------
    "Dumbbell_Preacher_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Machine_Preacher_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 3.0,
        "rounding":        5
    },
    "Scott_Curl": {
        "base_factor":     0.17,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Incline_Curl": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 2.0,
        "rounding":        2.5
    },
    "EZ_Bar_Curl": {
        "base_factor":     0.20,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 3.0,
        "rounding":        5
    },
    "Barbell_Curl": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 3.5,
        "rounding":        5
    },
    "Twist_Dumbbell_Curl": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 2.0,
        "rounding":        2.5
    },
    "Hammer_Preacher_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Spider_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Strict_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Face_Away_Bayesian_Cable_Curl": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        5
    },
    "Standard_Dumbbell_Curl": {
        "base_factor":     0.17,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        2.5
    },
    "Dumbbell_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        2.5
    },
    "Hammer_Curl": {
        "base_factor":     0.19,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 2.5,
        "rounding":        2.5
    },

    # ---------------- Chest ----------------
    "Bench_Press": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Inclined_Dumbbell_Press": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5
    },
    "Decline_Bench_Press": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Flat_Dumbbell_Press": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        2.5
    },
    "Cable_Crossover": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Pec_Deck": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 10,
        "rounding":        5
    },
    "Dembbell_Flye": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Hex_Press": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        2.5
    },
    "Guillottine_Press": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Push_up": {
        "base_factor":     0.18,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 6.0,
        "rounding":        2,
        "min_capacity":    5
    },
    "Deficit_Push_up": {
        "base_factor":     0.15,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 5.0,
        "rounding":        2,
        "min_capacity":    5
    },
    "Plyometric_Push_up": {
        "base_factor":     0.12,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 4.0,
        "rounding":        2,
        "min_capacity":    5
    },
    "1_Arm_Dumbbell_Press": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5.0,
        "rounding":        2.5
    },
    "Smith_Machine_Flat_Bench_Press": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Smith_Machine_Incline_Bench_Press": {
        "base_factor":     0.65,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Machine_Chest_Press": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5
    },
    "Dumbbell_Pullover": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5
    },

    # ---------------- Deadlift ----------------
    "Classic_Deadlift": {
        "base_factor":     0.6,
        "experience_factor": 10.0,
        "sex_male":        1.5,
        "sex_female":      1.0,
        "rounding":        5
    },
    "Sumo_Deadlift": {
        "base_factor":     0.65,
        "experience_factor": 8.75,
        "sex_male":        1.4,
        "sex_female":      0.95,
        "rounding":        5
    },

    # ---------------- Legs ----------------
    "Barbell_Back_Squat": {
        "base_factor":     0.85,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Barbell_Front_Squat": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Low_Bar_Squat": {
        "base_factor":     0.9,
        "sex_male":        1.0,
        "sex_female":      0.7,
        "experience_factor": 15,
        "rounding":        5
    },
    "Hack_Squat": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 20,
        "rounding":        5
    },
    "45_Degree_Leg_Press": {
        "base_factor":     1.2,
        "sex_male":        1.0,
        "sex_female":      0.7,
        "experience_factor": 25,
        "rounding":        10
    },
    "Lunge": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Leg_Extension": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5
    },
    "Goblet_Squat": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Bulgarian_Split_Squat": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Step_up": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Smith_Machine_Squat": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 15,
        "rounding":        5
    },

    # ---------------- Shoulder ----------------
    "Overhead_Press": {
        "base_factor":     0.5,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Dumbbell_Overhead_Press": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Overhead_Seated_Press": {
        "base_factor":     0.45,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Standing_Dumbbell_Lateral_Raise": {
        "base_factor":     0.12,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Cable_Lateral_Raise": {
        "base_factor":     0.10,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Lean_Away_Dumbbell_Lateral_Raise": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 3.0,
        "rounding":        2.5
    },
    "Lean_In_Dumbbell_Lateral_Raise": {
        "base_factor":     0.13,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Atlantis_Standing_Machine_Lateral_Raise": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        5
    },
    "Seated_Machine_Lateral_Raise": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        5
    },
    "Super_Row_Lateral_Raise": {
        "base_factor":     0.11,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 2.0,
        "rounding":        2.5
    },
    "Arnold_Style_Side_Lying_Dumbbell_Press": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Reverse_Pec_Deck": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Rope_Facepull": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Bent_Over_Reverse_Dumbbell_Flye": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Upright_Row": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Cable_Y_Raise": {
        "base_factor":     0.1,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Front_Raise": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 3.0,
        "rounding":        2.5
    },

    # ---------------- Triceps ----------------
    "Close_Grip_Dips": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Overhead_Cable_Triceps_Extension": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Overhead_Cable_Triceps_Extension_Rope": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        5
    },
    "Triceps_Press_down": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Triceps_Press_down_Bar": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Triceps_Press_down_Reverse_Grip": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        5
    },
    "Skull_crusher": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Dumbbell_Skull_crusher": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "JM_Press": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Dumbbell_French_Press": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "1_Arm_Dumbbell_Overhead_Extention": {
        "base_factor":     0.12,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 3.0,
        "rounding":        2.5
    },
    "Cable_Triceps_Kickback": {
        "base_factor":     0.10,
        "sex_male":        1.0,
        "sex_female":      0.4,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Dumbbell_Triceps_Kickback": {
        "base_factor":     0.11,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 2.5,
        "rounding":        2.5
    },
    "Bench_Dips": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        5
    },
    "Close_Grip_Push_up": {
        "base_factor":     0.16,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 5.0,
        "rounding":        2,
        "min_capacity":    5
    },
    "Diamond_Push_up": {
        "base_factor":     0.14,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 4.0,
        "rounding":        2,
        "min_capacity":    5
    }
}

# Create estimator objects
estimators = {ex: ExerciseEstimator(**params) for ex, params in exercise_config.items()}

def estimate_exercise(exercise_name, WE, SX, EX):
    return estimators[exercise_name].estimate(WE, SX, EX)

if __name__ == "__main__":
    person = collect_user_data()
    
   # Back
print("Waited Pull up:", estimate_exercise("Waited_Pull_up", person.WE, person.SX, person.EX))
print("Neutral Grip Pull up:", estimate_exercise("Neutral_Grip_Pull_up", person.WE, person.SX, person.EX))
print("Chin up:", estimate_exercise("Chin_up", person.WE, person.SX, person.EX))
print("Barbell Row:", estimate_exercise("Barbell_Row", person.WE, person.SX, person.EX))
print("Pendlay Row:", estimate_exercise("Pendlay_Row", person.WE, person.SX, person.EX))
print("Dumbbell Row:", estimate_exercise("Dumbbell_Row", person.WE, person.SX, person.EX))
print("Chest Supported T Bar Row:", estimate_exercise("Chest_Supported_T_Bar_Row", person.WE, person.SX, person.EX))
print("Cable Row:", estimate_exercise("Cable_Row", person.WE, person.SX, person.EX))
print("Wide Grip Pull up A:", estimate_exercise("Wide_Grip_Pull_up_A", person.WE, person.SX, person.EX))
print("Wide Grip Pull up B:", estimate_exercise("Wide_Grip_Pull_up_B", person.WE, person.SX, person.EX))
print("Wide Grip Lat Pull down:", estimate_exercise("Wide_Grip_Lat_Pull_down", person.WE, person.SX, person.EX))
print("Neutral Grip Lat Pull down:", estimate_exercise("Neutral_Grip_Lat_Pull_down", person.WE, person.SX, person.EX))
print("Half Kneeling 1 Arm Lat Pull down A:", estimate_exercise("Half_Kneeling_1_Arm_Lat_Pull_down_A", person.WE, person.SX, person.EX))
print("Half Kneeling 1 Arm Lat Pull down B:", estimate_exercise("Half_Kneeling_1_Arm_Lat_Pull_down_B", person.WE, person.SX, person.EX))
print("Cable Lat Pullover:", estimate_exercise("Cable_Lat_Pullover", person.WE, person.SX, person.EX))
print("Meadows Row:", estimate_exercise("Meadows_Row", person.WE, person.SX, person.EX))
print("Free Standing T Bar Row:", estimate_exercise("Free_Standing_T_Bar_Row", person.WE, person.SX, person.EX))
print("Wide Grip Cable Row:", estimate_exercise("Wide_Grip_Cable_Row", person.WE, person.SX, person.EX))
# Biceps
print("Dumbbell Preacher Curl:", estimate_exercise("Dumbbell_Preacher_Curl", person.WE, person.SX, person.EX))
print("Machine Preacher Curl:", estimate_exercise("Machine_Preacher_Curl", person.WE, person.SX, person.EX))
print("Scott Curl:", estimate_exercise("Scott_Curl", person.WE, person.SX, person.EX))
print("Incline Curl:", estimate_exercise("Incline_Curl", person.WE, person.SX, person.EX))
print("EZ Bar Curl:", estimate_exercise("EZ_Bar_Curl", person.WE, person.SX, person.EX))
print("Barbell Curl:", estimate_exercise("Barbell_Curl", person.WE, person.SX, person.EX))
print("Twist Dumbbell Curl:", estimate_exercise("Twist_Dumbbell_Curl", person.WE, person.SX, person.EX))
print("Hammer Preacher Curl:", estimate_exercise("Hammer_Preacher_Curl", person.WE, person.SX, person.EX))
print("Spider Curl:", estimate_exercise("Spider_Curl", person.WE, person.SX, person.EX))
print("Strict Curl:", estimate_exercise("Strict_Curl", person.WE, person.SX, person.EX))
print("Face Away Bayesian Cable Curl:", estimate_exercise("Face_Away_Bayesian_Cable_Curl", person.WE, person.SX, person.EX))
print("Standard Dumbbell Curl:", estimate_exercise("Standard_Dumbbell_Curl", person.WE, person.SX, person.EX))
print("Dumbbell Curl:", estimate_exercise("Dumbbell_Curl", person.WE, person.SX, person.EX))
print("Hammer Curl:", estimate_exercise("Hammer_Curl", person.WE, person.SX, person.EX))
# Chest
print("Bench Press:", estimate_exercise("Bench_Press", person.WE, person.SX, person.EX))
print("Inclined Dumbbell Press:", estimate_exercise("Inclined_Dumbbell_Press", person.WE, person.SX, person.EX))
print("Decline Bench Press:", estimate_exercise("Decline_Bench_Press", person.WE, person.SX, person.EX))
print("Flat Dumbbell Press:", estimate_exercise("Flat_Dumbbell_Press", person.WE, person.SX, person.EX))
print("Cable Crossover:", estimate_exercise("Cable_Crossover", person.WE, person.SX, person.EX))
print("Pec Deck:", estimate_exercise("Pec_Deck", person.WE, person.SX, person.EX))
print("Dembbell Flye:", estimate_exercise("Dembbell_Flye", person.WE, person.SX, person.EX))
print("Hex Press:", estimate_exercise("Hex_Press", person.WE, person.SX, person.EX))
print("Guillottine Press:", estimate_exercise("Guillottine_Press", person.WE, person.SX, person.EX))
print("Push up:", estimate_exercise("Push_up", person.WE, person.SX, person.EX))
print("Deficit Push up:", estimate_exercise("Deficit_Push_up", person.WE, person.SX, person.EX))
print("Plyometric Push up:", estimate_exercise("Plyometric_Push_up", person.WE, person.SX, person.EX))
print("1 Arm Dumbbell Press:", estimate_exercise("1_Arm_Dumbbell_Press", person.WE, person.SX, person.EX))
print("Smith Machine Flat Bench Press:", estimate_exercise("Smith_Machine_Flat_Bench_Press", person.WE, person.SX, person.EX))
print("Smith Machine Incline Bench Press:", estimate_exercise("Smith_Machine_Incline_Bench_Press", person.WE, person.SX, person.EX))
print("Machine Chest Press:", estimate_exercise("Machine_Chest_Press", person.WE, person.SX, person.EX))
print("Dumbbell Pullover:", estimate_exercise("Dumbbell_Pullover", person.WE, person.SX, person.EX))
# Deadlift
print("Classic Deadlift:", estimate_exercise("Classic_Deadlift", person.WE, person.SX, person.EX))
print("Sumo Deadlift:", estimate_exercise("Sumo_Deadlift", person.WE, person.SX, person.EX))
# Legs
print("Barbell Back Squat:", estimate_exercise("Barbell_Back_Squat", person.WE, person.SX, person.EX))
print("Barbell Front Squat:", estimate_exercise("Barbell_Front_Squat", person.WE, person.SX, person.EX))
print("Low Bar Squat:", estimate_exercise("Low_Bar_Squat", person.WE, person.SX, person.EX))
print("Bulgarian Split Squat:", estimate_exercise("Bulgarian_Split_Squat", person.WE, person.SX, person.EX))
print("Lunge:", estimate_exercise("Lunge", person.WE, person.SX, person.EX))
print("Hack Squat:", estimate_exercise("Hack_Squat", person.WE, person.SX, person.EX))
print("45 Degree Leg Press:", estimate_exercise("45_Degree_Leg_Press", person.WE, person.SX, person.EX))
print("Goblet Squat:", estimate_exercise("Goblet_Squat", person.WE, person.SX, person.EX))
print("Step up:", estimate_exercise("Step_up", person.WE, person.SX, person.EX))
print("Smith Machine Squat:", estimate_exercise("Smith_Machine_Squat", person.WE, person.SX, person.EX))
print("Leg Extension:", estimate_exercise("Leg_Extension", person.WE, person.SX, person.EX))
# Shoulder
print("Overhead Press:", estimate_exercise("Overhead_Press", person.WE, person.SX, person.EX))
print("Dumbbell Overhead Press:", estimate_exercise("Dumbbell_Overhead_Press", person.WE, person.SX, person.EX))
print("Overhead Seated Press:", estimate_exercise("Overhead_Seated_Press", person.WE, person.SX, person.EX))
print("Standing Dumbbell Lateral Raise:", estimate_exercise("Standing_Dumbbell_Lateral_Raise", person.WE, person.SX, person.EX))
print("Cable Lateral Raise:", estimate_exercise("Cable_Lateral_Raise", person.WE, person.SX, person.EX))
print("Lean Away Dumbbell Lateral Raise:", estimate_exercise("Lean_Away_Dumbbell_Lateral_Raise", person.WE, person.SX, person.EX))
print("Lean In Dumbbell Lateral Raise:", estimate_exercise("Lean_In_Dumbbell_Lateral_Raise", person.WE, person.SX, person.EX))
print("Atlantis Standing Machine Lateral Raise:", estimate_exercise("Atlantis_Standing_Machine_Lateral_Raise", person.WE, person.SX, person.EX))
print("Seated Machine Lateral Raise:", estimate_exercise("Seated_Machine_Lateral_Raise", person.WE, person.SX, person.EX))
print("Super Row Lateral Raise:", estimate_exercise("Super_Row_Lateral_Raise", person.WE, person.SX, person.EX))
print("Arnold Style Side Lying Dumbbell Press:", estimate_exercise("Arnold_Style_Side_Lying_Dumbbell_Press", person.WE, person.SX, person.EX))
print("Reverse Pec Deck:", estimate_exercise("Reverse_Pec_Deck", person.WE, person.SX, person.EX))
print("Rope Facepull:", estimate_exercise("Rope_Facepull", person.WE, person.SX, person.EX))
print("Bent Over Reverse Dumbbell Flye:", estimate_exercise("Bent_Over_Reverse_Dumbbell_Flye", person.WE, person.SX, person.EX))
print("Upright Row:", estimate_exercise("Upright_Row", person.WE, person.SX, person.EX))
print("Cable Y Raise:", estimate_exercise("Cable_Y_Raise", person.WE, person.SX, person.EX))
print("Front Raise:", estimate_exercise("Front_Raise", person.WE, person.SX, person.EX))
# Triceps
print("Close Grip Dips:", estimate_exercise("Close_Grip_Dips", person.WE, person.SX, person.EX))
print("Overhead Cable Triceps Extension:", estimate_exercise("Overhead_Cable_Triceps_Extension", person.WE, person.SX, person.EX))
print("Overhead Cable Triceps Extension Rope:", estimate_exercise("Overhead_Cable_Triceps_Extension_Rope", person.WE, person.SX, person.EX))
print("Triceps Press down:", estimate_exercise("Triceps_Press_down", person.WE, person.SX, person.EX))
print("Triceps Press down Bar:", estimate_exercise("Triceps_Press_down_Bar", person.WE, person.SX, person.EX))
print("Triceps Press down Reverse Grip:", estimate_exercise("Triceps_Press_down_Reverse_Grip", person.WE, person.SX, person.EX))
print("Skull crusher:", estimate_exercise("Skull_crusher", person.WE, person.SX, person.EX))
print("Dumbbell Skull crusher:", estimate_exercise("Dumbbell_Skull_crusher", person.WE, person.SX, person.EX))
print("JM Press:", estimate_exercise("JM_Press", person.WE, person.SX, person.EX))
print("Dumbbell French Press:", estimate_exercise("Dumbbell_French_Press", person.WE, person.SX, person.EX))
print("1 Arm Dumbbell Overhead Extention:", estimate_exercise("1_Arm_Dumbbell_Overhead_Extention", person.WE, person.SX, person.EX))
print("Cable Triceps Kickback:", estimate_exercise("Cable_Triceps_Kickback", person.WE, person.SX, person.EX))
print("Dumbbell Triceps Kickback:", estimate_exercise("Dumbbell_Triceps_Kickback", person.WE, person.SX, person.EX))
print("Bench Dips:", estimate_exercise("Bench_Dips", person.WE, person.SX, person.EX))
print("Close Grip Push up:", estimate_exercise("Close_Grip_Push_up", person.WE, person.SX, person.EX))
print("Diamond Push up:", estimate_exercise("Diamond_Push_up", person.WE, person.SX, person.EX))