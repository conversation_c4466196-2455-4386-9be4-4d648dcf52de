class Chest:
    def __init__(self):
        self.exercises = [
            "Bench_Press", "Inclined_Dumbbell_Press", "Decline_Bench_Press", "Flat_Dumbbell_Press",
            "Cable_Crossover", "Pec_Deck", "<PERSON>mbbell_Flye", "Hex_Press", "Guillottine_Press", "Push_up",
            "Deficit_Push_up", "Plyometric_Push_up", "1_Arm_Dumbbell_Press", "Smith_Machine_Flat_Bench_Press",
            "Smith_Machine_Incline_Bench_Press", "Machine_Chest_Press", "Dumbbell_Pullover"
        ]

class Back:
    def __init__(self):
        self.exercises = [
            "Waited_Pull_up", "Neutral_Grip_Pull_up", "Chin_up", "Barbell_Row", "Pendlay_Row", "Dumbbell_Row",
            "Chest_Supported_T_Bar_Row", "Cable_Row", "Wide_Grip_Pull_up_A", "Wide_Grip_Pull_up_B",
            "Wide_Grip_Lat_Pull_down", "Neutral_Grip_Lat_Pull_down", "Half_Kneeling_1_Arm_Lat_Pull_down_A",
            "Half_Kneeling_1_Arm_Lat_Pull_down_B", "Cable_Lat_Pullover", "Meadows_Row",
            "Free_Standing_T_Bar_Row", "Wide_Grip_Cable_Row"
        ]

class Shoulders:
    def __init__(self):
        self.exercises = [
            "Overhead_Press", "Dumbbell_Overhead_Press", "Overhead_Seated_Press", "Standing_Dumbbell_Lateral_Raise",
            "Cable_Lateral_Raise", "Lean_Away_Dumbbell_Lateral_Raise", "Lean_In_Dumbbell_Lateral_Raise",
            "Atlantis_Standing_Machine_Lateral_Raise", "Seated_Machine_Lateral_Raise", "Super_Row_Lateral_Raise",
            "Arnold_Style_Side_Lying_Dumbbell_Press", "Reverse_Pec_Deck", "Rope_Facepull",
            "Bent_Over_Reverse_Dumbbell_Flye", "Upright_Row", "Cable_Y_Raise", "Front_Raise"
        ]

class Biceps:
    def __init__(self):
        self.exercises = [
            "Dumbbell_Preacher_Curl", "Machine_Preacher_Curl", "Scott_Curl", "Incline_Curl", "EZ_Bar_Curl",
            "Barbell_Curl", "Twist_Dumbbell_Curl", "Hammer_Preacher_Curl", "Spider_Curl", "Strict_Curl",
            "Face_Away_Bayesian_Cable_Curl", "Standard_Dumbbell_Curl", "Dumbbell_Curl", "Hammer_Curl"
        ]

class Triceps:
    def __init__(self):
        self.exercises = [
            "Close_Grip_Dips", "Overhead_Cable_Triceps_Extension", "Overhead_Cable_Triceps_Extension_Rope",
            "Triceps_Press_down", "Triceps_Press_down_Bar", "Triceps_Press_down_Reverse_Grip", "Skull_crusher",
            "Dumbbell_Skull_crusher", "JM_Press", "Dumbbell_French_Press", "1_Arm_Dumbbell_Overhead_Extention",
            "Cable_Triceps_Kickback", "Dumbbell_Triceps_Kickback", "Bench_Dips", "Close_Grip_Push_up",
            "Demand_Push_up"
        ]

class Deadlift:
    def __init__(self):
        self.exercises = [
            "Classic_Deadlift", "Sumo_Deadlift"
        ]

class Legs:
    def __init__(self):
        self.exercises = [
            "Barbell_Back_Squat", "Barbell_Front_Squat", "Low_Bar_Squat", "Bulgarian_Split_Squat", "Lunge",
            "Hack_Squat", "45_Degree_Leg_Press", "Goblet_Squat", "Step_up", "Smith_Machine_Squat", "Leg_Extension"
        ]

# Main Exercise class
class Exercise:
    def __init__(self):
        self.chest = Chest()
        self.back = Back()
        self.shoulders = Shoulders()
        self.biceps = Biceps()
        self.triceps = Triceps()
        self.deadlift = Deadlift()
        self.legs = Legs()

# Example usage
E = Exercise()

# Print test: First 3 chest exercises
print(E.chest.exercises[:3])

# Print a few example exercises
print(E.back.exercises[0])     # Wide_Grip_Pull_up_A
print(E.biceps.exercises[5])   # Twist_Dumbbell_Curl
print(E.chest.exercises[2])    # Bench_Press
