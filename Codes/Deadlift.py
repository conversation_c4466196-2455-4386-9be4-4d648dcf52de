def predict_deadlift_formula(WE, SX, EX):
    base = 0.6
    step = 0.4
    
    sex_multiplier = 0  # default
    if SX == 1:
        sex_multiplier = 1.5
    if SX == 0:
        sex_multiplier = 1

    # Calculate raw deadlift
    raw_strength = WE * (base + EX * step) * sex_multiplier

    # Round to nearest 5 kg
    rounded_strength = round(raw_strength / 5) * 5

    return rounded_strength

# Example usage
WE = int(input("Enter body weight (kg): "))
SX = int(input("Enter sex (1 = male, 0 = female): "))
EX = int(input("Enter experience (0 = beginner, 1 = intermediate, 2 = advanced): "))

result = predict_deadlift_formula(WE, SX, EX)
print(f"Estimated Deadlift: {result} kg")
