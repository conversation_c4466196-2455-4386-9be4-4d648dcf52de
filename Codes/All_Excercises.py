class ExerciseEstimator:
    def __init__(self,
                 base_factor,
                 sex_male,
                 sex_female,
                 experience_factor,
                 rounding=5,
                 min_capacity=None):
        self.base_factor       = base_factor
        self.sex_male          = sex_male
        self.sex_female        = sex_female
        self.experience_factor = experience_factor
        self.rounding          = rounding
        self.min_capacity      = min_capacity

    def estimate(self, WE, SX, EX):
        sex_multiplier = self.sex_male if SX == 1 else self.sex_female
        raw_value = (WE * self.base_factor + EX * self.experience_factor) * sex_multiplier
        rounded_value = round(raw_value / self.rounding) * self.rounding

        if self.min_capacity is not None:
            return max(rounded_value, self.min_capacity)
        return rounded_value


exercise_config = {
    # ---------------- Deadlift ----------------
    "Classic_Deadlift": {
        "base_factor":     0.6,
        "experience_factor": 10.0,  # ⬅️ new key
        "sex_male":        1.5,
        "sex_female":      1.0,
        "rounding":        5
    },
    "Sumo_Deadlift": {
        "base_factor":     0.65,
        "experience_factor": 8.75,  # ⬅️ new key
        "sex_male":        1.4,
        "sex_female":      0.95,
        "rounding":        5
    }
}
# --------------------------------------------------------
# USAGE
# --------------------------------------------------------

estimators = {ex: ExerciseEstimator(**params) for ex, params in exercise_config.items()}

def estimate_exercise(exercise_name, WE, SX, EX):
    return estimators[exercise_name].estimate(WE, SX, EX)

# Example usage:
print("Classic Deadlift:", estimate_exercise("Classic_Deadlift", 75, 1, 2))
