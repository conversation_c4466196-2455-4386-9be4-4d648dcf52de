def predict_situp_capacity_formula(WE, SX, EX):

    base_factor = 0.18

    sex_multiplier = 0  # default
    if SX == 1:
        sex_multiplier = 1.2
    if SX == 0:
        sex_multiplier = 0.6

    # Calculate raw sit-up capacity
    raw_capacity = (WE * base_factor + EX * 6.0) * sex_multiplier

    # Round to nearest multiple of 2
    rounded_capacity = round(raw_capacity / 2) * 2

    # Minimum capacity is 5
    final_capacity = max(rounded_capacity, 5)

    return final_capacity

# Example usage
WE = int(input("Enter body weight (kg): "))
SX = int(input("Enter sex (1 = male, 0 = female): "))
EX = int(input("Enter experience (0 = beginner, 1 = intermediate, 2 = advanced): "))

result = predict_situp_capacity_formula(WE, SX, EX)
print(f"Estimated pushup: {result}")
