def predict_lat_pulldown_formula(WE, SX, EX):

    base_factor = 0.8

    sex_multiplier = 0  # default
    if SX == 1:
        sex_multiplier = 1.0
    if SX == 0:
        sex_multiplier = 0.6

    # Calculate raw lat pulldown
    raw_strength = (WE * base_factor + EX * 15.0) * sex_multiplier

    # Round to nearest multiple of 5
    rounded_strength = round(raw_strength / 5) * 5

    return rounded_strength

# Example usage
WE = int(input("Enter body weight (kg): "))
SX = int(input("Enter sex (1 = male, 0 = female): "))
EX = int(input("Enter experience (0 = beginner, 1 = intermediate, 2 = advanced): "))

result = predict_lat_pulldown_formula(WE, SX, EX)
print(f"Estimated Lat Pulldown: {result} kg")
