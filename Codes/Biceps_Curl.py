def predict_biceps_curl_formula(WE, SX, EX):

    base_factor = 0.15

    sex_multiplier = 0  # default
    if SX == 1:
        sex_multiplier = 1.0
    if SX == 0:
        sex_multiplier = 0.6

    # Calculate raw biceps curl
    raw_strength = (WE * base_factor + EX * 2.0) * sex_multiplier

    # Round to nearest multiple of 2.5
    rounded_strength = round(raw_strength / 2.5) * 2.5

    return rounded_strength

# Example usage
WE = int(input("Enter body weight (kg): "))
SX = int(input("Enter sex (1 = male, 0 = female): "))
EX = int(input("Enter experience (0 = beginner, 1 = intermediate, 2 = advanced): "))

result = predict_biceps_curl_formula(WE, SX, EX)
print(f"Estimated Biceps Curl: {result} kg")
