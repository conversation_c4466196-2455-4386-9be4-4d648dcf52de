def predict_bench_press_formula(WE, SX, EX):

    base_factor = 0.75

    sex_multiplier = 0  # default
    if SX == 1:
        sex_multiplier = 1.0
    if SX == 0:
        sex_multiplier = 0.6

    raw_strength = (WE * base_factor + EX * 10) * sex_multiplier

    # Round to nearest multiple of 5
    rounded_strength = round(raw_strength / 5) * 5

    return rounded_strength

# Example usage
WE = int(input("Enter body weight (kg): "))
SX = int(input("Enter sex (1 = male, 0 = female): "))
EX = int(input("Enter experience (0 = beginner, 1 = intermediate, 2 = advanced): "))

result = predict_bench_press_formula(WE, SX, EX)
print(f"Estimated Bench Press: {result} kg")
