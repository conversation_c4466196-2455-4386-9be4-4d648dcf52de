import numpy as np

class Person:
    def __init__(self, NA, AG, SX, HT, WE, PU, EX, DF, GL, DT):
        self.NA = NA
        self.AG = AG
        self.SX = SX
        self.HT = HT
        self.WE = WE
        self.PU = PU
        self.EX = EX
        self.DF = DF
        self.GL = GL
        self.DT = DT

def collect_user_data():
    print("Enter your details:\n")

    NA = "Ankit"
    AG = 21
    SX = int(input("Sex (1 = Male, 0 = Female): "))
    HT = 200
    WE = int(input("Body Weight (kg): "))
    PU = 100
    EX = int(input("Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced): "))
    DF = 0
    GL = int(input("Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training): "))
    DT = 1

    return Person(NA, AG, SX, HT, WE, PU, EX, DF, GL, DT)

class ExerciseEstimator:

    def __init__(self, base_factor, sex_male, sex_female, experience_factor, rounding=5, min_capacity=None, **kwargs):
        self.base_factor = base_factor
        self.sex_male = sex_male
        self.sex_female = sex_female
        self.experience_factor = experience_factor
        self.rounding = rounding
        self.min_capacity = min_capacity

    def estimate(self, WE, SX, EX):
        sex_factor = self.sex_male if SX == 1 else self.sex_female
        value = WE * self.base_factor * sex_factor + EX * self.experience_factor
        value = round(value / self.rounding) * self.rounding
        if self.min_capacity is not None and value < self.min_capacity:
            value = self.min_capacity
        return value

exercise_config = {
    # ---------------- Back ----------------
    "Waited_Pull_up": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Neutral_Grip_Pull_up": {
        "base_factor":     0.28,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 6.0,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Chin_up": {
        "base_factor":     0.32,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 7.0,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Barbell_Row": {
        "base_factor":     0.65,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Pendlay_Row": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Dumbbell_Row": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Back"
    },
    "Chest_Supported_T_Bar_Row": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Cable_Row": {
        "base_factor":     0.55,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Wide_Grip_Pull_up_A": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 6.0,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Wide_Grip_Pull_up_B": {
        "base_factor":     0.27,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 6.5,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Wide_Grip_Lat_Pull_down": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Neutral_Grip_Lat_Pull_down": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Half_Kneeling_1_Arm_Lat_Pull_down_A": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 8,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Half_Kneeling_1_Arm_Lat_Pull_down_B": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Cable_Lat_Pullover": {
        "base_factor":     0.45,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Meadows_Row": {
        "base_factor":     0.5,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Free_Standing_T_Bar_Row": {
        "base_factor":     0.55,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back"
    },
    "Wide_Grip_Cable_Row": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Back"
    },

    # ---------------- Biceps ----------------
    "Dumbbell_Preacher_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    "Machine_Preacher_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 3.0,
        "rounding":        5,
        "muscle_group": "Biceps"
    },
    "Scott_Curl": {
        "base_factor":     0.17,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    "Incline_Curl": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    "EZ_Bar_Curl": {
        "base_factor":     0.20,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 3.0,
        "rounding":        5,
        "muscle_group": "Biceps"
    },
    "Barbell_Curl": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 3.5,
        "rounding":        5,
        "muscle_group": "Biceps"
    },
    "Twist_Dumbbell_Curl": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    "Hammer_Preacher_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    "Spider_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    "Strict_Curl": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    "Face_Away_Bayesian_Cable_Curl": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        5,
        "muscle_group": "Biceps"
    },
    "Standard_Dumbbell_Curl": {
        "base_factor":     0.17,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    "Dumbbell_Curl": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    "Hammer_Curl": {
        "base_factor":     0.19,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Biceps"
    },
    
    # ---------------- Chest ----------------
    "Bench_Press": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Chest"
    },
    "Inclined_Dumbbell_Press": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Chest"
    },
    "Decline_Bench_Press": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Chest"
    },
    "Flat_Dumbbell_Press": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Chest"
    },
    "Cable_Crossover": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Chest"
    },
    "Pec_Deck": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Chest"
    },
    "Dembbell_Flye": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Chest"
    },
    "Hex_Press": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Chest"
    },
    "Guillottine_Press": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Chest"
    },
    "Push_up": {
        "base_factor":     0.18,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 6.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Chest"
    },
    "Deficit_Push_up": {
        "base_factor":     0.15,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 5.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Chest"
    },
    "Plyometric_Push_up": {
        "base_factor":     0.12,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 4.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Chest"
    },
    "1_Arm_Dumbbell_Press": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5.0,
        "rounding":        2.5,
        "muscle_group": "Chest"
    },
    "Smith_Machine_Flat_Bench_Press": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Chest"
    },
    "Smith_Machine_Incline_Bench_Press": {
        "base_factor":     0.65,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Chest"
    },
    "Machine_Chest_Press": {
        "base_factor":     0.6,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Chest"
    },
    "Dumbbell_Pullover": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        2.5,
        "muscle_group": "Chest"
    },

    # ---------------- Deadlift ----------------
    "Classic_Deadlift": {
        "base_factor":     0.6,
        "experience_factor": 10.0,
        "sex_male":        1.5,
        "sex_female":      1.0,
        "rounding":        5,
        "muscle_group": "Deadlift"
    },
    "Sumo_Deadlift": {
        "base_factor":     0.65,
        "experience_factor": 8.75,
        "sex_male":        1.4,
        "sex_female":      0.95,
        "rounding":        5,
        "muscle_group": "Deadlift"
    },

    # ---------------- Legs ----------------
    "Barbell_Back_Squat": {
        "base_factor":     0.85,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 12.5,
        "rounding":        5,
        "muscle_group": "Legs"
    },
    "Barbell_Front_Squat": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Legs"
    },
    "Low_Bar_Squat": {
        "base_factor":     0.9,
        "sex_male":        1.0,
        "sex_female":      0.7,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Legs"
    },
    "Hack_Squat": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 20,
        "rounding":        5,
        "muscle_group": "Legs"
    },
    "45_Degree_Leg_Press": {
        "base_factor":     1.2,
        "sex_male":        1.0,
        "sex_female":      0.7,
        "experience_factor": 25,
        "rounding":        10,
        "muscle_group": "Legs"
    },
    "Lunge": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Legs"
    },
    "Leg_Extension": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Legs"
    },
    "Goblet_Squat": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Legs"
    },
    "Bulgarian_Split_Squat": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Legs"
    },
    "Step_up": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Legs"
    },
    "Smith_Machine_Squat": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 15,
        "rounding":        5,
        "muscle_group": "Legs"
    },

    # ---------------- Shoulder ----------------
    "Overhead_Press": {
        "base_factor":     0.5,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Shoulder"
    },
    "Dumbbell_Overhead_Press": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },
    "Overhead_Seated_Press": {
        "base_factor":     0.45,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Shoulder"
    },
    "Standing_Dumbbell_Lateral_Raise": {
        "base_factor":     0.12,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },
    "Cable_Lateral_Raise": {
        "base_factor":     0.10,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },
    "Lean_Away_Dumbbell_Lateral_Raise": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 3.0,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },
    "Lean_In_Dumbbell_Lateral_Raise": {
        "base_factor":     0.13,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },
    "Atlantis_Standing_Machine_Lateral_Raise": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Shoulder"
    },
    "Seated_Machine_Lateral_Raise": {
        "base_factor":     0.14,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Shoulder"
    },
    "Super_Row_Lateral_Raise": {
        "base_factor":     0.11,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 2.0,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },
    "Arnold_Style_Side_Lying_Dumbbell_Press": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },
    "Reverse_Pec_Deck": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Shoulder"
    },
    "Rope_Facepull": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Shoulder"
    },
    "Bent_Over_Reverse_Dumbbell_Flye": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },
    "Upright_Row": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Shoulder"
    },
    "Cable_Y_Raise": {
        "base_factor":     0.1,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },
    "Front_Raise": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 3.0,
        "rounding":        2.5,
        "muscle_group": "Shoulder"
    },

    # ---------------- Triceps ----------------
    "Close_Grip_Dips": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Triceps"
    },
    "Overhead_Cable_Triceps_Extension": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Triceps"
    },
    "Overhead_Cable_Triceps_Extension_Rope": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Triceps"
    },
    "Triceps_Press_down": {
        "base_factor":     0.22,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Triceps"
    },
    "Triceps_Press_down_Bar": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Triceps"
    },
    "Triceps_Press_down_Reverse_Grip": {
        "base_factor":     0.18,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Triceps"
    },
    "Skull_crusher": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 7.5,
        "rounding":        5,
        "muscle_group": "Triceps"
    },
    "Dumbbell_Skull_crusher": {
        "base_factor":     0.15,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Triceps"
    },
    "JM_Press": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5,
        "muscle_group": "Triceps"
    },
    "Dumbbell_French_Press": {
        "base_factor":     0.16,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5,
        "muscle_group": "Triceps"
    },
    "1_Arm_Dumbbell_Overhead_Extention": {
        "base_factor":     0.12,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 3.0,
        "rounding":        2.5,
        "muscle_group": "Triceps"
    },
    "Cable_Triceps_Kickback": {
        "base_factor":     0.10,
        "sex_male":        1.0,
        "sex_female":      0.4,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Triceps"
    },
    "Dumbbell_Triceps_Kickback": {
        "base_factor":     0.11,
        "sex_male":        1.0,
        "sex_female":      0.45,
        "experience_factor": 2.5,
        "rounding":        2.5,
        "muscle_group": "Triceps"
    },
    "Bench_Dips": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        5,
        "muscle_group": "Triceps"
    },
    "Close_Grip_Push_up": {
        "base_factor":     0.16,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 5.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Triceps"
    },
    "Diamond_Push_up": {
        "base_factor":     0.14,
        "sex_male":        1.2,
        "sex_female":      0.6,
        "experience_factor": 4.0,
        "rounding":        2,
        "min_capacity":    5,
        "muscle_group": "Triceps"
    }
}

exercise_names = list(exercise_config.keys())


exercise_weights = {
    "Back": [
        0.18,  # Barbell_Row
        0.16,  # Pendlay_Row
        0.14,  # Waited_Pull_up
        0.12,  # Dumbbell_Row
        0.10,  # Cable_Row
        0.08,  # Neutral_Grip_Pull_up
        0.08,  # Chin_up
        0.06,  # Chest_Supported_T_Bar_Row
        0.04,  # Wide_Grip_Pull_up_A
        0.04   # Wide_Grip_Pull_up_B
    ],
    "Biceps": [
        0.22,  # Barbell_Curl
        0.18,  # EZ_Bar_Curl
        0.16,  # Dumbbell_Curl
        0.12,  # Dumbbell_Preacher_Curl
        0.10,  # Machine_Preacher_Curl
        0.06,  # Incline_Curl
        0.05,  # Spider_Curl
        0.04,  # Scott_Curl
        0.04,  # Hammer_Preacher_Curl
        0.03   # Twist_Dumbbell_Curl
    ],
    "Chest": [
        0.25,  # Bench_Press
        0.20,  # Inclined_Dumbbell_Press
        0.15,  # Flat_Dumbbell_Press
        0.12,  # Decline_Bench_Press
        0.08,  # Machine_Chest_Press
        0.06,  # Cable_Crossover
        0.05,  # Pec_Deck
        0.04,  # Dembbell_Flye
        0.03,  # Push_up
        0.02   # Guillottine_Press
    ],
    "Deadlift": [0.6, 0.4],  # Classic_Deadlift, Sumo_Deadlift
    "Legs": [
        0.28,  # Barbell_Back_Squat
        0.22,  # Barbell_Front_Squat
        0.16,  # 45_Degree_Leg_Press
        0.12,  # Hack_Squat
        0.08,  # Bulgarian_Split_Squat
        0.05,  # Lunge
        0.04,  # Goblet_Squat
        0.03,  # Step_up
        0.01,  # Smith_Machine_Squat
        0.01   # Low_Bar_Squat
    ],
    "Shoulder": [
        0.22,  # Overhead_Press
        0.18,  # Dumbbell_Overhead_Press
        0.14,  # Overhead_Seated_Press
        0.10,  # Standing_Dumbbell_Lateral_Raise
        0.09,  # Cable_Lateral_Raise
        0.07,  # Reverse_Pec_Deck
        0.06,  # Rope_Facepull
        0.05,  # Bent_Over_Reverse_Dumbbell_Flye
        0.04,  # Upright_Row
        0.05   # Seated_Machine_Lateral_Raise
    ],
    "Triceps": [
        0.22,  # Close_Grip_Dips
        0.18,  # Triceps_Press_down
        0.15,  # Skull_crusher
        0.12,  # Overhead_Cable_Triceps_Extension
        0.10,  # Overhead_Cable_Triceps_Extension_Rope
        0.08,  # Triceps_Press_down_Bar
        0.06,  # Dumbbell_Skull_crusher
        0.04,  # JM_Press
        0.03,  # Dumbbell_French_Press
        0.02   # Triceps_Press_down_Reverse_Grip
    ]
}

def choose(muscle_group, n):
    elements = [ex for ex, cfg in exercise_config.items() if cfg.get("muscle_group") == muscle_group]
    # If you have weights for the muscle group, you can use them here
    weights = np.ones(len(elements)) / len(elements)  # or use custom weights if available
    return [str(e) for e in np.random.choice(elements, size=n, replace=False, p=weights)]
# Convert configs to estimator objects
estimators = {ex: ExerciseEstimator(**cfg) for ex, cfg in exercise_config.items()}

def estimate_exercise(name, WE, SX, EX):
    return estimators[name].estimate(WE, SX, EX)


if __name__ == "__main__":
    person = collect_user_data()
    print(f"\nHello {person.NA}! Here are your estimated lifts:\n")

      
    # Back
    print("Waited Pull up:", estimate_exercise("Waited_Pull_up", person.WE, person.SX, person.EX))
    print("Neutral Grip Pull up:", estimate_exercise("Neutral_Grip_Pull_up", person.WE, person.SX, person.EX))
    print("Chin up:", estimate_exercise("Chin_up", person.WE, person.SX, person.EX))
    print("Barbell Row:", estimate_exercise("Barbell_Row", person.WE, person.SX, person.EX))
    print("Pendlay Row:", estimate_exercise("Pendlay_Row", person.WE, person.SX, person.EX))
    print("Dumbbell Row:", estimate_exercise("Dumbbell_Row", person.WE, person.SX, person.EX))
    print("Chest Supported T Bar Row:", estimate_exercise("Chest_Supported_T_Bar_Row", person.WE, person.SX, person.EX))
    print("Cable Row:", estimate_exercise("Cable_Row", person.WE, person.SX, person.EX))
    print("Wide Grip Pull up A:", estimate_exercise("Wide_Grip_Pull_up_A", person.WE, person.SX, person.EX))
    print("Wide Grip Pull up B:", estimate_exercise("Wide_Grip_Pull_up_B", person.WE, person.SX, person.EX))
    print("Wide Grip Lat Pull down:", estimate_exercise("Wide_Grip_Lat_Pull_down", person.WE, person.SX, person.EX))
    print("Neutral Grip Lat Pull down:", estimate_exercise("Neutral_Grip_Lat_Pull_down", person.WE, person.SX, person.EX))
    print("Half Kneeling 1 Arm Lat Pull down A:", estimate_exercise("Half_Kneeling_1_Arm_Lat_Pull_down_A", person.WE, person.SX, person.EX))
    print("Half Kneeling 1 Arm Lat Pull down B:", estimate_exercise("Half_Kneeling_1_Arm_Lat_Pull_down_B", person.WE, person.SX, person.EX))
    print("Cable Lat Pullover:", estimate_exercise("Cable_Lat_Pullover", person.WE, person.SX, person.EX))
    print("Meadows Row:", estimate_exercise("Meadows_Row", person.WE, person.SX, person.EX))
    print("Free Standing T Bar Row:", estimate_exercise("Free_Standing_T_Bar_Row", person.WE, person.SX, person.EX))
    print("Wide Grip Cable Row:", estimate_exercise("Wide_Grip_Cable_Row", person.WE, person.SX, person.EX))
    # Biceps
    print("Dumbbell Preacher Curl:", estimate_exercise("Dumbbell_Preacher_Curl", person.WE, person.SX, person.EX))
    print("Machine Preacher Curl:", estimate_exercise("Machine_Preacher_Curl", person.WE, person.SX, person.EX))
    print("Scott Curl:", estimate_exercise("Scott_Curl", person.WE, person.SX, person.EX))
    print("Incline Curl:", estimate_exercise("Incline_Curl", person.WE, person.SX, person.EX))
    print("EZ Bar Curl:", estimate_exercise("EZ_Bar_Curl", person.WE, person.SX, person.EX))
    print("Barbell Curl:", estimate_exercise("Barbell_Curl", person.WE, person.SX, person.EX))
    print("Twist Dumbbell Curl:", estimate_exercise("Twist_Dumbbell_Curl", person.WE, person.SX, person.EX))
    print("Hammer Preacher Curl:", estimate_exercise("Hammer_Preacher_Curl", person.WE, person.SX, person.EX))
    print("Spider Curl:", estimate_exercise("Spider_Curl", person.WE, person.SX, person.EX))
    print("Strict Curl:", estimate_exercise("Strict_Curl", person.WE, person.SX, person.EX))
    print("Face Away Bayesian Cable Curl:", estimate_exercise("Face_Away_Bayesian_Cable_Curl", person.WE, person.SX, person.EX))
    print("Standard Dumbbell Curl:", estimate_exercise("Standard_Dumbbell_Curl", person.WE, person.SX, person.EX))
    print("Dumbbell Curl:", estimate_exercise("Dumbbell_Curl", person.WE, person.SX, person.EX))
    print("Hammer Curl:", estimate_exercise("Hammer_Curl", person.WE, person.SX, person.EX))
    # Chest
    print("Bench Press:", estimate_exercise("Bench_Press", person.WE, person.SX, person.EX))
    print("Inclined Dumbbell Press:", estimate_exercise("Inclined_Dumbbell_Press", person.WE, person.SX, person.EX))
    print("Decline Bench Press:", estimate_exercise("Decline_Bench_Press", person.WE, person.SX, person.EX))
    print("Flat Dumbbell Press:", estimate_exercise("Flat_Dumbbell_Press", person.WE, person.SX, person.EX))
    print("Cable Crossover:", estimate_exercise("Cable_Crossover", person.WE, person.SX, person.EX))
    print("Pec Deck:", estimate_exercise("Pec_Deck", person.WE, person.SX, person.EX))
    print("Dembbell Flye:", estimate_exercise("Dembbell_Flye", person.WE, person.SX, person.EX))
    print("Hex Press:", estimate_exercise("Hex_Press", person.WE, person.SX, person.EX))
    print("Guillottine Press:", estimate_exercise("Guillottine_Press", person.WE, person.SX, person.EX))
    print("Push up:", estimate_exercise("Push_up", person.WE, person.SX, person.EX))
    print("Deficit Push up:", estimate_exercise("Deficit_Push_up", person.WE, person.SX, person.EX))
    print("Plyometric Push up:", estimate_exercise("Plyometric_Push_up", person.WE, person.SX, person.EX))
    print("1 Arm Dumbbell Press:", estimate_exercise("1_Arm_Dumbbell_Press", person.WE, person.SX, person.EX))
    print("Smith Machine Flat Bench Press:", estimate_exercise("Smith_Machine_Flat_Bench_Press", person.WE, person.SX, person.EX))
    print("Smith Machine Incline Bench Press:", estimate_exercise("Smith_Machine_Incline_Bench_Press", person.WE, person.SX, person.EX))
    print("Machine Chest Press:", estimate_exercise("Machine_Chest_Press", person.WE, person.SX, person.EX))
    print("Dumbbell Pullover:", estimate_exercise("Dumbbell_Pullover", person.WE, person.SX, person.EX))
    # Deadlift
    print("Classic Deadlift:", estimate_exercise("Classic_Deadlift", person.WE, person.SX, person.EX))
    print("Sumo Deadlift:", estimate_exercise("Sumo_Deadlift", person.WE, person.SX, person.EX))
    # Legs
    print("Barbell Back Squat:", estimate_exercise("Barbell_Back_Squat", person.WE, person.SX, person.EX))
    print("Barbell Front Squat:", estimate_exercise("Barbell_Front_Squat", person.WE, person.SX, person.EX))
    print("Low Bar Squat:", estimate_exercise("Low_Bar_Squat", person.WE, person.SX, person.EX))
    print("Bulgarian Split Squat:", estimate_exercise("Bulgarian_Split_Squat", person.WE, person.SX, person.EX))
    print("Lunge:", estimate_exercise("Lunge", person.WE, person.SX, person.EX))
    print("Hack Squat:", estimate_exercise("Hack_Squat", person.WE, person.SX, person.EX))
    print("45 Degree Leg Press:", estimate_exercise("45_Degree_Leg_Press", person.WE, person.SX, person.EX))
    print("Goblet Squat:", estimate_exercise("Goblet_Squat", person.WE, person.SX, person.EX))
    print("Step up:", estimate_exercise("Step_up", person.WE, person.SX, person.EX))
    print("Smith Machine Squat:", estimate_exercise("Smith_Machine_Squat", person.WE, person.SX, person.EX))
    print("Leg Extension:", estimate_exercise("Leg_Extension", person.WE, person.SX, person.EX))
    # Shoulder
    print("Overhead Press:", estimate_exercise("Overhead_Press", person.WE, person.SX, person.EX))
    print("Dumbbell Overhead Press:", estimate_exercise("Dumbbell_Overhead_Press", person.WE, person.SX, person.EX))
    print("Overhead Seated Press:", estimate_exercise("Overhead_Seated_Press", person.WE, person.SX, person.EX))
    print("Standing Dumbbell Lateral Raise:", estimate_exercise("Standing_Dumbbell_Lateral_Raise", person.WE, person.SX, person.EX))
    print("Cable Lateral Raise:", estimate_exercise("Cable_Lateral_Raise", person.WE, person.SX, person.EX))
    print("Lean Away Dumbbell Lateral Raise:", estimate_exercise("Lean_Away_Dumbbell_Lateral_Raise", person.WE, person.SX, person.EX))
    print("Lean In Dumbbell Lateral Raise:", estimate_exercise("Lean_In_Dumbbell_Lateral_Raise", person.WE, person.SX, person.EX))
    print("Atlantis Standing Machine Lateral Raise:", estimate_exercise("Atlantis_Standing_Machine_Lateral_Raise", person.WE, person.SX, person.EX))
    print("Seated Machine Lateral Raise:", estimate_exercise("Seated_Machine_Lateral_Raise", person.WE, person.SX, person.EX))
    print("Super Row Lateral Raise:", estimate_exercise("Super_Row_Lateral_Raise", person.WE, person.SX, person.EX))
    print("Arnold Style Side Lying Dumbbell Press:", estimate_exercise("Arnold_Style_Side_Lying_Dumbbell_Press", person.WE, person.SX, person.EX))
    print("Reverse Pec Deck:", estimate_exercise("Reverse_Pec_Deck", person.WE, person.SX, person.EX))
    print("Rope Facepull:", estimate_exercise("Rope_Facepull", person.WE, person.SX, person.EX))
    print("Bent Over Reverse Dumbbell Flye:", estimate_exercise("Bent_Over_Reverse_Dumbbell_Flye", person.WE, person.SX, person.EX))
    print("Upright Row:", estimate_exercise("Upright_Row", person.WE, person.SX, person.EX))
    print("Cable Y Raise:", estimate_exercise("Cable_Y_Raise", person.WE, person.SX, person.EX))
    print("Front Raise:", estimate_exercise("Front_Raise", person.WE, person.SX, person.EX))
    # Triceps
    print("Close Grip Dips:", estimate_exercise("Close_Grip_Dips", person.WE, person.SX, person.EX))
    print("Overhead Cable Triceps Extension:", estimate_exercise("Overhead_Cable_Triceps_Extension", person.WE, person.SX, person.EX))
    print("Overhead Cable Triceps Extension Rope:", estimate_exercise("Overhead_Cable_Triceps_Extension_Rope", person.WE, person.SX, person.EX))
    print("Triceps Press down:", estimate_exercise("Triceps_Press_down", person.WE, person.SX, person.EX))
    print("Triceps Press down Bar:", estimate_exercise("Triceps_Press_down_Bar", person.WE, person.SX, person.EX))
    print("Triceps Press down Reverse Grip:", estimate_exercise("Triceps_Press_down_Reverse_Grip", person.WE, person.SX, person.EX))
    print("Skull crusher:", estimate_exercise("Skull_crusher", person.WE, person.SX, person.EX))
    print("Dumbbell Skull crusher:", estimate_exercise("Dumbbell_Skull_crusher", person.WE, person.SX, person.EX))
    print("JM Press:", estimate_exercise("JM_Press", person.WE, person.SX, person.EX))
    print("Dumbbell French Press:", estimate_exercise("Dumbbell_French_Press", person.WE, person.SX, person.EX))
    print("1 Arm Dumbbell Overhead Extention:", estimate_exercise("1_Arm_Dumbbell_Overhead_Extention", person.WE, person.SX, person.EX))
    print("Cable Triceps Kickback:", estimate_exercise("Cable_Triceps_Kickback", person.WE, person.SX, person.EX))
    print("Dumbbell Triceps Kickback:", estimate_exercise("Dumbbell_Triceps_Kickback", person.WE, person.SX, person.EX))
    print("Bench Dips:", estimate_exercise("Bench_Dips", person.WE, person.SX, person.EX))
    print("Close Grip Push up:", estimate_exercise("Close_Grip_Push_up", person.WE, person.SX, person.EX))
    print("Diamond Push up:", estimate_exercise("Diamond_Push_up", person.WE, person.SX, person.EX))
    
    print(choose("Biceps", 3))  # Output: 3 weighted random back exercises




