

class Person:
    def __init__(self, NA, AG, SX, HT, WE, PU, EX, DF, GL, DT):
        self.NA = NA     # Name
        self.AG = AG     # Age
        self.SX = SX     # Sex (1 = Male, 0 = Female)
        self.HT = HT     # Height (cm)
        self.WE = WE     # Body weight (kg)
        self.PU = PU     # Clean pushups
        self.EX = EX     # Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced)
        self.DF = DF     # Difficulty (-1 = Easy, 0 = Normal, 1 = Hard)
        self.GL = GL     # Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training)
        self.DT = DT     # Diet Type (-1 = Veg, 0 = Eggiterian, 1 = Non-veg)

def collect_user_data():
    print("Enter your details:\n")

    NA = "Ankit"
    AG = 21
    SX = int(input("Sex (1 = Male, 0 = Female): "))
    HT = 200
    WE = int(input("Body Weight (kg): "))
    PU = 100
    EX = int(input("Experience (0 = Beginner, 1 = Intermediate, 2 = Advanced): "))
    DF = 0
    GL = int(input("Goal (1 = Weight Gain, 2 = Weight Loss, 3 = Strength Training): "))
    DT = 1

    return Person(NA, AG, SX, HT, WE, PU, EX, DF, GL, DT)

class ExerciseEstimator:
    def __init__(self,
                 base_factor,
                 sex_male,
                 sex_female,
                 experience_factor,
                 rounding=5,
                 min_capacity=None):
        
        self.base_factor       = base_factor
        self.sex_male          = sex_male
        self.sex_female        = sex_female
        self.experience_factor = experience_factor
        self.rounding          = rounding
        self.min_capacity      = min_capacity

    def estimate(self, WE, SX, EX):
        
        # Choose sex factor
        sex_factor = self.sex_male if SX == 1 else self.sex_female

        # Calculate base value
        value = WE * self.base_factor * sex_factor

        # Add experience
        value += EX * self.experience_factor

        # Round to nearest rounding
        value = round(value / self.rounding) * self.rounding

        # Enforce minimum if specified
        if self.min_capacity is not None and value < self.min_capacity:
            value = self.min_capacity
        return value

exercise_config = {
    # ---------------- Legs ----------------
    "Barbell_Back_Squat": {
        "base_factor":     0.85,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 12.5,
        "rounding":        5
    },
    "Barbell_Front_Squat": {
        "base_factor":     0.75,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 10,
        "rounding":        5
    },
    "Low_Bar_Squat": {
        "base_factor":     0.9,
        "sex_male":        1.0,
        "sex_female":      0.7,
        "experience_factor": 15,
        "rounding":        5
    },
    "Hack_Squat": {
        "base_factor":     0.7,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 20,
        "rounding":        5
    },
    "45_Degree_Leg_Press": {
        "base_factor":     1.2,
        "sex_male":        1.0,
        "sex_female":      0.7,
        "experience_factor": 25,
        "rounding":        10
    },
    "Lunge": {
        "base_factor":     0.3,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Leg_Extension": {
        "base_factor":     0.4,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 15,
        "rounding":        5
    },
    "Goblet_Squat": {
        "base_factor":     0.35,
        "sex_male":        1.0,
        "sex_female":      0.6,
        "experience_factor": 7.5,
        "rounding":        5
    },
    "Bulgarian_Split_Squat": {
        "base_factor":     0.25,
        "sex_male":        1.0,
        "sex_female":      0.55,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Step_up": {
        "base_factor":     0.2,
        "sex_male":        1.0,
        "sex_female":      0.5,
        "experience_factor": 5,
        "rounding":        2.5
    },
    "Smith_Machine_Squat": {
        "base_factor":     0.8,
        "sex_male":        1.0,
        "sex_female":      0.65,
        "experience_factor": 15,
        "rounding":        5
    }
}

# Create estimator objects
estimators = {ex: ExerciseEstimator(**params) for ex, params in exercise_config.items()}

def estimate_exercise(exercise_name, WE, SX, EX):
    return estimators[exercise_name].estimate(WE, SX, EX)

if __name__ == "__main__":
    person = collect_user_data()


    # Legs
print("Barbell Back Squat:", estimate_exercise("Barbell_Back_Squat", person.WE, person.SX, person.EX))
print("Barbell Front Squat:", estimate_exercise("Barbell_Front_Squat", person.WE, person.SX, person.EX))
print("Low Bar Squat:", estimate_exercise("Low_Bar_Squat", person.WE, person.SX, person.EX))
print("Bulgarian Split Squat:", estimate_exercise("Bulgarian_Split_Squat", person.WE, person.SX, person.EX))
print("Lunge:", estimate_exercise("Lunge", person.WE, person.SX, person.EX))
print("Hack Squat:", estimate_exercise("Hack_Squat", person.WE, person.SX, person.EX))
print("45 Degree Leg Press:", estimate_exercise("45_Degree_Leg_Press", person.WE, person.SX, person.EX))
print("Goblet Squat:", estimate_exercise("Goblet_Squat", person.WE, person.SX, person.EX))
print("Step up:", estimate_exercise("Step_up", person.WE, person.SX, person.EX))
print("Smith Machine Squat:", estimate_exercise("Smith_Machine_Squat", person.WE, person.SX, person.EX))
print("Leg Extension:", estimate_exercise("Leg_Extension", person.WE, person.SX, person.EX))
